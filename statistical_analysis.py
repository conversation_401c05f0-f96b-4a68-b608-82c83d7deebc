import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

# 读取数据
file_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/02_Data_And_Analysis/Raw_Data/Raw_Data_2025-08-04_16-20.csv'
df = pd.read_csv(file_path)

print("数据基本信息:")
print(f"样本数量: {len(df)}")
print(f"特征数量: {len(df.columns)}")
print(f"时间范围: {df['date_time'].iloc[0]} 到 {df['date_time'].iloc[-1]}")

# 清理数据 - 移除空白列
df = df.loc[:, ~df.columns.str.contains('^Unnamed')]

# 关键变量分析
key_vars = ['s1_percent_current', 's2_percent_current', 'electrode_pressure', 
            'gas_fill_pressure', 'post_weld_disk_holder_height', 'weld_failure_type']

print("\n关键变量统计:")
print(df[key_vars].describe())

# 失效模式分析
print("\n失效模式分布:")
failure_counts = df['weld_failure_type'].value_counts()
print(failure_counts)

# 工艺类型分析
print("\n工艺类型分布:")
schedule_counts = df['schedule_type'].value_counts()
print(schedule_counts)

# 深入统计分析
print("\n=== 深入统计分析 ===")

# 1. 失效模式与工艺类型的关系
print("\n失效模式与工艺类型交叉分析:")
failure_schedule_crosstab = pd.crosstab(df['weld_failure_type'], df['schedule_type'])
print(failure_schedule_crosstab)

# 2. 关键参数与失效模式的关系
print("\n关键参数与失效模式的关系:")
numeric_cols = ['s1_percent_current', 's2_percent_current', 'electrode_pressure', 'gas_fill_pressure', 'post_weld_disk_holder_height']
for col in numeric_cols:
    if col in df.columns:
        print(f"\n{col} 按失效模式分组统计:")
        failure_group_stats = df.groupby('weld_failure_type')[col].describe()
        print(failure_group_stats)

# 3. Type 4失效（高度不良）的专门分析
print("\n=== Type 4失效专门分析 ===")
type4_data = df[df['weld_failure_type'] == 4]
print(f"Type 4失效样本数: {len(type4_data)}")
print(f"Type 4失效样本中工艺类型分布:")
print(type4_data['schedule_type'].value_counts())

if len(type4_data) > 0:
    print("Type 4失效样本的关键参数统计:")
    for col in numeric_cols:
        if col in type4_data.columns:
            try:
                # 尝试转换为数值
                numeric_data = pd.to_numeric(type4_data[col], errors='coerce')
                if numeric_data.notna().sum() > 0:
                    print(f"{col}: mean={numeric_data.mean():.2f}, std={numeric_data.std():.2f}")
                else:
                    print(f"{col}: 无法转换为数值数据")
            except:
                print(f"{col}: 数据转换失败")

# 4. 成功样本分析
print("\n=== 成功样本分析 ===")
success_data = df[df['weld_failure_type'] == 1]  # 假设1表示成功
print(f"成功样本数: {len(success_data)}")
print(f"成功样本中工艺类型分布:")
print(success_data['schedule_type'].value_counts())

if len(success_data) > 0:
    print("成功样本的关键参数统计:")
    for col in numeric_cols:
        if col in success_data.columns:
            try:
                # 尝试转换为数值
                numeric_data = pd.to_numeric(success_data[col], errors='coerce')
                if numeric_data.notna().sum() > 0:
                    print(f"{col}: mean={numeric_data.mean():.2f}, std={numeric_data.std():.2f}")
                else:
                    print(f"{col}: 无法转换为数值数据")
            except:
                print(f"{col}: 数据转换失败")