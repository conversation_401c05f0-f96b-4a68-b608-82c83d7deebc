import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 读取8月4日数据文件
df_new = pd.read_csv('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/02_Data_And_Analysis/Raw_Data/Raw_Data_2025-08-04_16-20.csv')
df_new = df_new.dropna(axis=1, how='all')
df_new = df_new.loc[:, ~df_new.columns.str.contains('^Unnamed')]
df_valid_new = df_new[df_new['verification'] == 'Rocky'].copy()

# 数据类型转换
numeric_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b', 
                'gas_fill_pressure', 'electrode_pressure', 's1_percent_current', 
                's2_percent_current', 's1_hold', 's2_hold', 'room_temperature',
                'disk_gap_a', 'disk_gap_b', 'push_out_force']

for col in numeric_cols:
    if col in df_valid_new.columns:
        df_valid_new[col] = pd.to_numeric(df_valid_new[col], errors='coerce')

# 时间分组
df_valid_new['datetime'] = pd.to_datetime(df_valid_new['date_time'], errors='coerce')
df_valid_new['date'] = df_valid_new['datetime'].dt.date

print('=== G3P OP60 8月4日数据全面分析 ===')
print(f'总样本数: {len(df_valid_new)}')

# 识别8月4日的新数据
august_4_data = df_valid_new[df_valid_new['date'] == pd.Timestamp('2025-08-04').date()]
print(f'8月4日新增样本数: {len(august_4_data)}')

if len(august_4_data) > 0:
    print(f'8月4日样本组号: {list(august_4_data["group#"].unique())}')
    print(f'8月4日样本ID范围: {august_4_data["sample_id"].min()} - {august_4_data["sample_id"].max()}')

    print('\n=== 1. 数据完整性评估 ===')
    
    # 测试路径分析
    print('\n测试路径分析:')
    
    # 金相分析路径 (有焊缝宽度和间隙数据)
    metallographic_samples = df_valid_new[
        (df_valid_new['weld_width_a'].notna()) & 
        (df_valid_new['disk_gap_a'].notna()) &
        (df_valid_new['weld_width_a'] > 0) &
        (df_valid_new['disk_gap_a'] >= 0)
    ]
    print(f'金相分析路径样本数: {len(metallographic_samples)}')
    
    # 推出测试路径
    push_out_samples = df_valid_new[df_valid_new['push_out_force'].notna()]
    print(f'推出测试路径样本数: {len(push_out_samples)}')
    
    print('\n=== 2. 8月4日新数据分析 ===')
    
    print('\n8月4日总体质量表现:')
    aug4_success_rate = (august_4_data['weld_failure_type'] == 1.0).mean() * 100
    print(f'成功率: {aug4_success_rate:.1f}%')
    
    # 失效类型分布
    print('\n失效类型分布:')
    failure_dist = august_4_data['weld_failure_type'].value_counts().sort_index()
    for failure_type, count in failure_dist.items():
        percentage = count / len(august_4_data) * 100
        failure_name = {1.0: 'Type 1 (成功)', 4.0: 'Type 4 (高度失效)', 
                       8.0: 'Type 8 (裂纹失效)', 5.0: 'Type 5 (宽度失效)', 
                       6.0: 'Type 6 (脱落失效)'}.get(failure_type, f'Type {failure_type}')
        print(f'  {failure_name}: {count}个 ({percentage:.1f}%)')
    
    # 参数分析
    print('\n8月4日参数设置分析:')
    key_params = ['electrode_pressure', 's1_percent_current', 's1_hold', 's2_hold']
    for param in key_params:
        if param in august_4_data.columns:
            unique_values = august_4_data[param].dropna().unique()
            print(f'  {param}: {sorted(unique_values)}')
    
    # 质量指标统计
    print('\n8月4日质量指标:')
    quality_metrics = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b']
    for metric in quality_metrics:
        if metric in august_4_data.columns:
            data = august_4_data[metric].dropna()
            if len(data) > 0:
                print(f'  {metric}: 均值{data.mean():.3f} ± {data.std():.3f} (范围: {data.min():.3f}-{data.max():.3f})')

    print('\n=== 3. 与之前数据对比 ===')
    
    # 8月1-3日数据
    aug_1_3_data = df_valid_new[
        (df_valid_new['date'] >= pd.Timestamp('2025-08-01').date()) &
        (df_valid_new['date'] <= pd.Timestamp('2025-08-03').date())
    ]
    
    print(f'\n8月1-3日样本数: {len(aug_1_3_data)}')
    print(f'8月4日样本数: {len(august_4_data)}')
    
    if len(aug_1_3_data) > 0:
        aug_1_3_success = (aug_1_3_data['weld_failure_type'] == 1.0).mean() * 100
        aug_4_success = (august_4_data['weld_failure_type'] == 1.0).mean() * 100
        
        print(f'\n成功率对比:')
        print(f'  8月1-3日: {aug_1_3_success:.1f}%')
        print(f'  8月4日: {aug_4_success:.1f}%')
        print(f'  改进幅度: {aug_4_success - aug_1_3_success:+.1f}个百分点')

    print('\n=== 4. 参数效应验证 ===')
    
    # 基于8月4日数据验证参数效应
    if len(august_4_data) > 0:
        print('\n8月4日参数效应分析:')
        
        # 电极压力效应
        if 'electrode_pressure' in august_4_data.columns:
            pressure_effect = august_4_data.groupby('electrode_pressure').agg({
                'weld_failure_type': ['count', lambda x: (x==1.0).mean()*100]
            }).round(1)
            pressure_effect.columns = ['样本数', '成功率(%)']
            print('\n电极压力效应:')
            print(pressure_effect)
        
        # S1电流效应
        if 's1_percent_current' in august_4_data.columns:
            current_effect = august_4_data.groupby('s1_percent_current').agg({
                'weld_failure_type': ['count', lambda x: (x==1.0).mean()*100]
            }).round(1)
            current_effect.columns = ['样本数', '成功率(%)']
            print('\nS1电流效应:')
            print(current_effect)
        
        # S1保持时间效应
        if 's1_hold' in august_4_data.columns:
            hold_effect = august_4_data.groupby('s1_hold').agg({
                'weld_failure_type': ['count', lambda x: (x==1.0).mean()*100]
            }).round(1)
            hold_effect.columns = ['样本数', '成功率(%)']
            print('\nS1保持时间效应:')
            print(hold_effect)

else:
    print('未找到8月4日的数据')
