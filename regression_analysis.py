import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.linear_model import LinearRegression, LogisticRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, f_regression, chi2
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
file_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/02_Data_And_Analysis/Raw_Data/Raw_Data_2025-08-04_16-20.csv'
df = pd.read_csv(file_path)

# 清理数据 - 移除空白列
df = df.loc[:, ~df.columns.str.contains('^Unnamed')]

print("=== 回归模型分析 ===")
print(f"原始数据形状: {df.shape}")

# 数据预处理
def preprocess_data(df):
    # 创建数据副本
    data = df.copy()
    
    # 转换数值列
    numeric_cols = ['s1_percent_current', 's2_percent_current', 'electrode_pressure', 
                   'gas_fill_pressure', 'post_weld_disk_holder_height', 'room_temperature',
                   'electrode_height', 's1_squeeze', 's1_weld_heat', 's1_hold', 's1_off',
                   's1_impulses', 's1_cool', 's2_squeeze', 's2_weld_heat', 's2_hold', 
                   's2_off', 's2_impulses', 's2_cool']
    
    for col in numeric_cols:
        if col in data.columns:
            data[col] = pd.to_numeric(data[col], errors='coerce')
    
    # 处理工艺类型
    if 'schedule_type' in data.columns:
        schedule_dummies = pd.get_dummies(data['schedule_type'], prefix='schedule')
        data = pd.concat([data, schedule_dummies], axis=1)
    
    # 创建失效类型二值变量 (1=成功, 0=失效)
    data['is_success'] = (data['weld_failure_type'] == 1).astype(int)
    data['is_type4_failure'] = (data['weld_failure_type'] == 4).astype(int)
    
    # 创建交互特征
    data['current_ratio'] = data['s1_percent_current'] / (data['s2_percent_current'] + 1)
    data['pressure_ratio'] = data['electrode_pressure'] / (data['gas_fill_pressure'] / 1000)
    data['heat_input'] = data['s1_percent_current'] * data['s1_weld_heat'] + data['s2_percent_current'] * data['s2_weld_heat']
    
    return data

# 执行预处理
processed_data = preprocess_data(df)
print(f"预处理后数据形状: {processed_data.shape}")

# 选择特征
feature_cols = ['s1_percent_current', 's2_percent_current', 'electrode_pressure', 
               'gas_fill_pressure', 'room_temperature', 'electrode_height',
               's1_squeeze', 's1_weld_heat', 's1_hold', 's1_off', 's1_impulses', 's1_cool',
               's2_squeeze', 's2_weld_heat', 's2_hold', 's2_off', 's2_impulses', 's2_cool',
               'current_ratio', 'pressure_ratio', 'heat_input',
               'schedule_double_s1s2', 'schedule_single_s1', 'schedule_double_s1']

# 移除包含NaN的行
model_data = processed_data.dropna(subset=feature_cols + ['post_weld_disk_holder_height', 'is_success'])
print(f"模型数据形状: {model_data.shape}")

X = model_data[feature_cols]
y_height = model_data['post_weld_disk_holder_height']
y_success = model_data['is_success']
y_type4 = model_data['is_type4_failure']

print(f"特征数量: {len(feature_cols)}")
print(f"高度目标变量统计: mean={y_height.mean():.3f}, std={y_height.std():.3f}")
print(f"成功率: {y_success.mean():.3f}")
print(f"Type 4失效率: {y_type4.mean():.3f}")

# 数据分割
X_train, X_test, y_height_train, y_height_test = train_test_split(X, y_height, test_size=0.3, random_state=42)
X_train_cls, X_test_cls, y_success_train, y_success_test = train_test_split(X, y_success, test_size=0.3, random_state=42)

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 特征标准化
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)
X_train_cls_scaled = scaler.fit_transform(X_train_cls)
X_test_cls_scaled = scaler.transform(X_test_cls)

# === 1. 焊接高度预测回归模型 ===
print("\n=== 1. 焊接高度预测回归模型 ===")

# 线性回归模型
lr_model = LinearRegression()
lr_model.fit(X_train_scaled, y_height_train)
y_height_pred_lr = lr_model.predict(X_test_scaled)

# 随机森林回归模型
rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
rf_model.fit(X_train_scaled, y_height_train)
y_height_pred_rf = rf_model.predict(X_test_scaled)

# 岭回归模型
ridge_model = Ridge(alpha=1.0)
ridge_model.fit(X_train_scaled, y_height_train)
y_height_pred_ridge = ridge_model.predict(X_test_scaled)

# 模型评估
def evaluate_regression_model(y_true, y_pred, model_name):
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    r2 = r2_score(y_true, y_pred)
    print(f"{model_name}:")
    print(f"  MSE: {mse:.6f}")
    print(f"  RMSE: {rmse:.6f}")
    print(f"  R²: {r2:.6f}")
    return mse, rmse, r2

print("回归模型性能比较:")
lr_metrics = evaluate_regression_model(y_height_test, y_height_pred_lr, "线性回归")
rf_metrics = evaluate_regression_model(y_height_test, y_height_pred_rf, "随机森林")
ridge_metrics = evaluate_regression_model(y_height_test, y_height_pred_ridge, "岭回归")

# 特征重要性分析
print("\n=== 特征重要性分析 ===")
feature_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': rf_model.feature_importances_
}).sort_values('importance', ascending=False)

print("随机森林特征重要性 (Top 10):")
print(feature_importance.head(10))

# 线性回归系数
lr_coefficients = pd.DataFrame({
    'feature': feature_cols,
    'coefficient': lr_model.coef_
}).sort_values('coefficient', key=abs, ascending=False)

print("\n线性回归系数 (Top 10):")
print(lr_coefficients.head(10))

# 交叉验证
print("\n=== 交叉验证结果 ===")
cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_height_train, cv=5, scoring='r2')
cv_scores_rf = cross_val_score(rf_model, X_train_scaled, y_height_train, cv=5, scoring='r2')
print(f"线性回归 CV R²: {cv_scores_lr.mean():.6f} ± {cv_scores_lr.std():.6f}")
print(f"随机森林 CV R²: {cv_scores_rf.mean():.6f} ± {cv_scores_rf.std():.6f}")

# 预测值可视化
plt.figure(figsize=(12, 5))
plt.subplot(1, 2, 1)
plt.scatter(y_height_test, y_height_pred_lr, alpha=0.6, label='Linear Regression')
plt.plot([y_height_test.min(), y_height_test.max()], [y_height_test.min(), y_height_test.max()], 'r--', lw=2)
plt.xlabel('Actual Height')
plt.ylabel('Predicted Height')
plt.title('Linear Regression: Actual vs Predicted')
plt.legend()

plt.subplot(1, 2, 2)
plt.scatter(y_height_test, y_height_pred_rf, alpha=0.6, label='Random Forest', color='green')
plt.plot([y_height_test.min(), y_height_test.max()], [y_height_test.min(), y_height_test.max()], 'r--', lw=2)
plt.xlabel('Actual Height')
plt.ylabel('Predicted Height')
plt.title('Random Forest: Actual vs Predicted')
plt.legend()

plt.tight_layout()
plt.savefig('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/height_prediction_comparison.png')
plt.close()

print("高度预测对比图已保存")

# === 2. 失效概率预测模型 ===
print("\n=== 2. 失效概率预测模型 ===")

# 逻辑回归模型
logreg_model = LogisticRegression(random_state=42, max_iter=1000)
logreg_model.fit(X_train_cls_scaled, y_success_train)
y_success_pred_logreg = logreg_model.predict(X_test_cls_scaled)

# 随机森林分类模型
rf_cls_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_cls_model.fit(X_train_cls_scaled, y_success_train)
y_success_pred_rf = rf_cls_model.predict(X_test_cls_scaled)

# 模型评估
def evaluate_classification_model(y_true, y_pred, model_name):
    accuracy = accuracy_score(y_true, y_pred)
    print(f"{model_name}:")
    print(f"  准确率: {accuracy:.6f}")
    print(f"  分类报告:")
    print(classification_report(y_true, y_pred))
    return accuracy

print("分类模型性能比较:")
logreg_acc = evaluate_classification_model(y_success_test, y_success_pred_logreg, "逻辑回归")
rf_acc = evaluate_classification_model(y_success_test, y_success_pred_rf, "随机森林")

# 特征重要性（分类）
cls_feature_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': rf_cls_model.feature_importances_
}).sort_values('importance', ascending=False)

print("\n随机森林分类特征重要性 (Top 10):")
print(cls_feature_importance.head(10))

# 逻辑回归系数
logreg_coefficients = pd.DataFrame({
    'feature': feature_cols,
    'coefficient': logreg_model.coef_[0]
}).sort_values('coefficient', key=abs, ascending=False)

print("\n逻辑回归系数 (Top 10):")
print(logreg_coefficients.head(10))

# 交叉验证（分类）
print("\n=== 分类模型交叉验证结果 ===")
cv_scores_logreg = cross_val_score(logreg_model, X_train_cls_scaled, y_success_train, cv=5, scoring='accuracy')
cv_scores_rf_cls = cross_val_score(rf_cls_model, X_train_cls_scaled, y_success_train, cv=5, scoring='accuracy')
print(f"逻辑回归 CV 准确率: {cv_scores_logreg.mean():.6f} ± {cv_scores_logreg.std():.6f}")
print(f"随机森林 CV 准确率: {cv_scores_rf_cls.mean():.6f} ± {cv_scores_rf_cls.std():.6f}")

# Type 4失效专门预测
print("\n=== Type 4失效专门预测 ===")
X_type4, _, y_type4_train, y_type4_test = train_test_split(X, y_type4, test_size=0.3, random_state=42)
X_type4_train_scaled = scaler.fit_transform(X_type4)
X_type4_test_scaled = scaler.transform(X_test)

# Type 4预测模型
type4_model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
type4_model.fit(X_type4_train_scaled, y_type4_train)
y_type4_pred = type4_model.predict(X_type4_test_scaled)

print("Type 4失效预测性能:")
type4_accuracy = accuracy_score(y_type4_test, y_type4_pred)
print(f"准确率: {type4_accuracy:.6f}")
print("分类报告:")
print(classification_report(y_type4_test, y_type4_pred))

# Type 4特征重要性
type4_importance = pd.DataFrame({
    'feature': feature_cols,
    'importance': type4_model.feature_importances_
}).sort_values('importance', ascending=False)

print("\nType 4失效预测特征重要性 (Top 10):")
print(type4_importance.head(10))

# === 3. 模型验证和性能评估 ===
print("\n=== 3. 模型验证和性能评估 ===")

# 模型比较总结
model_comparison = pd.DataFrame({
    'Model': ['Linear Regression', 'Random Forest Regression', 'Ridge Regression', 
              'Logistic Regression', 'Random Forest Classification', 'Type 4 Prediction'],
    'Target': ['Height', 'Height', 'Height', 'Success', 'Success', 'Type 4 Failure'],
    'Metric': ['R²', 'R²', 'R²', 'Accuracy', 'Accuracy', 'Accuracy'],
    'Score': [lr_metrics[2], rf_metrics[2], ridge_metrics[2], 
              logreg_acc, rf_acc, type4_accuracy],
    'CV_Score': [cv_scores_lr.mean(), cv_scores_rf.mean(), 'N/A',
                 cv_scores_logreg.mean(), cv_scores_rf_cls.mean(), 'N/A']
})

print("模型性能总结:")
print(model_comparison)

# 残差分析
plt.figure(figsize=(12, 4))
plt.subplot(1, 3, 1)
residuals_lr = y_height_test - y_height_pred_lr
plt.scatter(y_height_pred_lr, residuals_lr, alpha=0.6)
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('Predicted Height')
plt.ylabel('Residuals')
plt.title('Linear Regression Residuals')

plt.subplot(1, 3, 2)
residuals_rf = y_height_test - y_height_pred_rf
plt.scatter(y_height_pred_rf, residuals_rf, alpha=0.6, color='green')
plt.axhline(y=0, color='r', linestyle='--')
plt.xlabel('Predicted Height')
plt.ylabel('Residuals')
plt.title('Random Forest Residuals')

plt.subplot(1, 3, 3)
plt.hist(residuals_lr, bins=20, alpha=0.7, label='Linear Regression')
plt.hist(residuals_rf, bins=20, alpha=0.7, label='Random Forest')
plt.xlabel('Residuals')
plt.ylabel('Frequency')
plt.title('Residuals Distribution')
plt.legend()

plt.tight_layout()
plt.savefig('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/residual_analysis.png')
plt.close()

print("残差分析图已保存")

# 混淆矩阵可视化
plt.figure(figsize=(12, 4))
plt.subplot(1, 3, 1)
cm_logreg = confusion_matrix(y_success_test, y_success_pred_logreg)
sns.heatmap(cm_logreg, annot=True, fmt='d', cmap='Blues')
plt.title('Logistic Regression Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')

plt.subplot(1, 3, 2)
cm_rf = confusion_matrix(y_success_test, y_success_pred_rf)
sns.heatmap(cm_rf, annot=True, fmt='d', cmap='Greens')
plt.title('Random Forest Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')

plt.subplot(1, 3, 3)
cm_type4 = confusion_matrix(y_type4_test, y_type4_pred)
sns.heatmap(cm_type4, annot=True, fmt='d', cmap='Reds')
plt.title('Type 4 Failure Confusion Matrix')
plt.xlabel('Predicted')
plt.ylabel('Actual')

plt.tight_layout()
plt.savefig('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/confusion_matrices.png')
plt.close()

print("混淆矩阵图已保存")

# === 4. 关键参数敏感性分析 ===
print("\n=== 4. 关键参数敏感性分析 ===")

# 选择关键参数进行敏感性分析
key_params = ['s1_percent_current', 's2_percent_current', 'electrode_pressure', 'gas_fill_pressure']
param_ranges = {
    's1_percent_current': np.linspace(85, 99, 15),
    's2_percent_current': np.linspace(85, 99, 15),
    'electrode_pressure': np.linspace(40, 48, 17),
    'gas_fill_pressure': np.linspace(5000, 5200, 21)
}

# 使用最佳模型（线性回归）进行敏感性分析
sensitivity_results = {}

for param in key_params:
    if param in feature_cols:
        print(f"\n{param} 敏感性分析:")
        
        # 创建基准数据（使用中位数）
        baseline_data = X.median().values.reshape(1, -1)
        baseline_scaled = scaler.transform(baseline_data)
        
        # 改变目标参数
        param_index = feature_cols.index(param)
        predictions = []
        
        for value in param_ranges[param]:
            test_data = baseline_data.copy()
            test_data[0, param_index] = value
            test_scaled = scaler.transform(test_data)
            pred_height = lr_model.predict(test_scaled)[0]
            predictions.append(pred_height)
        
        # 计算敏感性
        sensitivity = np.std(predictions) / np.mean(predictions) * 100
        sensitivity_results[param] = sensitivity
        
        print(f"  敏感性系数: {sensitivity:.2f}%")
        print(f"  高度变化范围: {min(predictions):.3f} - {max(predictions):.3f} mm")
        
        # 可视化
        plt.figure(figsize=(8, 6))
        plt.plot(param_ranges[param], predictions, 'b-', linewidth=2, marker='o')
        plt.xlabel(param)
        plt.ylabel('Predicted Height (mm)')
        plt.title(f'{param} Sensitivity Analysis')
        plt.grid(True, alpha=0.3)
        
        # 标记目标高度范围
        plt.axhline(y=2.0, color='g', linestyle='--', label='Target Height (2.0mm)')
        plt.axhline(y=2.3, color='r', linestyle='--', label='Max Acceptable (2.3mm)')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig(f'/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/sensitivity_{param}.png')
        plt.close()

print(f"\n敏感性分析结果汇总:")
sensitivity_df = pd.DataFrame(list(sensitivity_results.items()), columns=['Parameter', 'Sensitivity (%)'])
sensitivity_df = sensitivity_df.sort_values('Sensitivity (%)', ascending=False)
print(sensitivity_df)

# 最优参数建议
print("\n=== 最优参数建议 ===")
print("基于回归模型分析，建议的最优参数范围:")

# 寻找使预测高度最接近2.0mm的参数组合
optimal_params = {}
for param in key_params:
    if param in feature_cols:
        param_index = feature_cols.index(param)
        baseline_data = X.median().values.reshape(1, -1)
        predictions = []
        
        for value in param_ranges[param]:
            test_data = baseline_data.copy()
            test_data[0, param_index] = value
            test_scaled = scaler.transform(test_data)
            pred_height = lr_model.predict(test_scaled)[0]
            predictions.append((value, pred_height))
        
        # 找到最接近2.0mm的值
        best_value = min(predictions, key=lambda x: abs(x[1] - 2.0))[0]
        optimal_params[param] = best_value
        print(f"{param}: {best_value:.1f}")

# 验证最优参数
print("\n最优参数组合验证:")
optimal_data = X.median().values.reshape(1, -1)
for param, value in optimal_params.items():
    param_index = feature_cols.index(param)
    optimal_data[0, param_index] = value

optimal_scaled = scaler.transform(optimal_data)
optimal_height = lr_model.predict(optimal_scaled)[0]
print(f"预测高度: {optimal_height:.3f} mm")

# 最终总结
print("\n=== 回归分析总结 ===")
print("1. 焊接高度预测模型:")
print(f"   - 最佳模型: 线性回归 (R² = {lr_metrics[2]:.3f})")
print(f"   - RMSE: {lr_metrics[1]:.3f} mm")

print("\n2. 失效预测模型:")
print(f"   - 成功预测最佳准确率: {logreg_acc:.3f}")
print(f"   - Type 4失效预测准确率: {type4_accuracy:.3f}")

print("\n3. 关键影响因素:")
print("   - s2_squeeze (最重要参数)")
print("   - pressure_ratio (压力比)")
print("   - gas_fill_pressure (充气压力)")
print("   - heat_input (热输入)")

print("\n4. 敏感性分析:")
most_sensitive = sensitivity_df.iloc[0]['Parameter']
sensitivity_value = sensitivity_df.iloc[0]['Sensitivity (%)']
print(f"   - 最敏感参数: {most_sensitive} ({sensitivity_value:.2f}%)")

print("\n5. 建议优化方向:")
print("   - 控制s2_squeeze时间以优化热管理")
print("   - 调整压力比以平衡焊接效果")
print("   - 在约束范围内优化充气压力")
print("   - 监控热输入以避免过热")