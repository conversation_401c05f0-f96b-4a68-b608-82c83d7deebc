# G3P OP60 DOE v7.1 第一阶段(H1假设验证)简化统计分析
# 分析日期: 2025年8月2日
# 数据源: NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv

import csv
import math

def load_stage1_data():
    """加载第一阶段H1验证数据"""
    print("=== 加载DOE v7.1第一阶段数据 ===")
    
    stage1_data = []
    
    with open('NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv', 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # 筛选H1阶段数据
            if row.get('group#', '').startswith('H1-'):
                stage1_data.append(row)
    
    print(f"H1阶段数据行数: {len(stage1_data)}")
    
    # 显示前几行数据
    print("\n=== H1阶段数据概览 ===")
    for i, row in enumerate(stage1_data[:5]):
        print(f"样本{i+1}: {row['group#']}, S1电流: {row['s1_percent_current']}%, 失效类型: {row['weld_failure_type']}, 高度: {row['post_weld_disk_holder_height']}mm")
    
    return stage1_data

def analyze_h1_hypothesis(data):
    """H1假设验证分析"""
    print("\n" + "="*60)
    print("H1假设验证分析: S1电流对Type 8裂纹失效的影响")
    print("="*60)
    
    # 分组数据
    group_97 = []
    group_99 = []
    
    for row in data:
        try:
            s1_current = float(row['s1_percent_current'])
            if s1_current == 97:
                group_97.append(row)
            elif s1_current == 99:
                group_99.append(row)
        except (ValueError, TypeError):
            continue
    
    print(f"\n97%电流组样本数: {len(group_97)}")
    print(f"99%电流组样本数: {len(group_99)}")
    
    # 分析失效类型分布
    def count_failure_types(group):
        counts = {'1': 0, '4': 0, '8': 0, 'other': 0}
        for row in group:
            failure_type = row['weld_failure_type']
            if failure_type in counts:
                counts[failure_type] += 1
            else:
                counts['other'] += 1
        return counts
    
    failure_97 = count_failure_types(group_97)
    failure_99 = count_failure_types(group_99)
    
    print("\n=== 失效类型分布分析 ===")
    
    # 97%组分析
    success_rate_97 = (failure_97['1'] / len(group_97)) * 100 if len(group_97) > 0 else 0
    type4_rate_97 = (failure_97['4'] / len(group_97)) * 100 if len(group_97) > 0 else 0
    type8_rate_97 = (failure_97['8'] / len(group_97)) * 100 if len(group_97) > 0 else 0
    
    print(f"\n97%电流组 (n={len(group_97)}):")
    print(f"  Type 1 (成功): {failure_97['1']}件 ({success_rate_97:.1f}%)")
    print(f"  Type 4 (高度失效): {failure_97['4']}件 ({type4_rate_97:.1f}%)")
    print(f"  Type 8 (裂纹失效): {failure_97['8']}件 ({type8_rate_97:.1f}%)")
    
    # 99%组分析
    success_rate_99 = (failure_99['1'] / len(group_99)) * 100 if len(group_99) > 0 else 0
    type4_rate_99 = (failure_99['4'] / len(group_99)) * 100 if len(group_99) > 0 else 0
    type8_rate_99 = (failure_99['8'] / len(group_99)) * 100 if len(group_99) > 0 else 0
    
    print(f"\n99%电流组 (n={len(group_99)}):")
    print(f"  Type 1 (成功): {failure_99['1']}件 ({success_rate_99:.1f}%)")
    print(f"  Type 4 (高度失效): {failure_99['4']}件 ({type4_rate_99:.1f}%)")
    print(f"  Type 8 (裂纹失效): {failure_99['8']}件 ({type8_rate_99:.1f}%)")
    
    # 简化统计检验
    print("\n=== H1假设统计检验 ===")
    
    type8_97 = failure_97['8']
    type8_99 = failure_99['8']
    
    print(f"\n列联表 (Type 8裂纹失效):")
    print(f"                Type 8    非Type 8    总计")
    print(f"97%电流组        {type8_97:2d}        {len(group_97)-type8_97:2d}       {len(group_97)}")
    print(f"99%电流组        {type8_99:2d}        {len(group_99)-type8_99:2d}       {len(group_99)}")
    
    # 简化的统计分析
    if type8_97 + type8_99 > 0:
        print(f"\nType 8失效率差异: {type8_rate_99 - type8_rate_97:.1f}个百分点")
        if type8_99 > type8_97:
            print(f"观察结果: 99%电流组Type 8失效率更高")
        elif type8_97 > type8_99:
            print(f"观察结果: 97%电流组Type 8失效率更高")
        else:
            print(f"观察结果: 两组Type 8失效率相同")
    else:
        print(f"\n⚠️ 注意: 两组均无Type 8失效")
    
    return {
        'group_97_n': len(group_97),
        'group_99_n': len(group_99),
        'success_rate_97': success_rate_97,
        'success_rate_99': success_rate_99,
        'type4_rate_97': type4_rate_97,
        'type4_rate_99': type4_rate_99,
        'type8_rate_97': type8_rate_97,
        'type8_rate_99': type8_rate_99,
        'group_97': group_97,
        'group_99': group_99
    }

def analyze_weld_height(h1_results):
    """焊接高度分析"""
    print("\n" + "="*60)
    print("焊后高度分析")
    print("="*60)
    
    group_97 = h1_results['group_97']
    group_99 = h1_results['group_99']
    
    # 提取高度数据
    def extract_heights(group):
        heights = []
        for row in group:
            try:
                height = float(row['post_weld_disk_holder_height'])
                if height > 0:
                    heights.append(height)
            except (ValueError, TypeError):
                continue
        return heights
    
    heights_97 = extract_heights(group_97)
    heights_99 = extract_heights(group_99)
    
    # 计算统计量
    def calc_stats(heights):
        if not heights:
            return {'mean': 0, 'std': 0, 'min': 0, 'max': 0}
        
        mean = sum(heights) / len(heights)
        variance = sum((x - mean) ** 2 for x in heights) / (len(heights) - 1) if len(heights) > 1 else 0
        std = math.sqrt(variance)
        
        return {
            'mean': mean,
            'std': std,
            'min': min(heights),
            'max': max(heights)
        }
    
    stats_97 = calc_stats(heights_97)
    stats_99 = calc_stats(heights_99)
    
    print(f"\n=== 焊接高度描述性统计 ===")
    print(f"97%电流组 (n={len(heights_97)}):")
    print(f"  平均值: {stats_97['mean']:.3f} mm")
    print(f"  标准差: {stats_97['std']:.3f} mm")
    print(f"  范围: {stats_97['min']:.3f} - {stats_97['max']:.3f} mm")
    
    # 目标范围符合率
    in_range_97 = sum(1 for h in heights_97 if 2.1 <= h <= 2.4)
    compliance_97 = (in_range_97 / len(heights_97)) * 100 if len(heights_97) > 0 else 0
    print(f"  目标范围符合率: {in_range_97}/{len(heights_97)} ({compliance_97:.1f}%)")
    
    print(f"\n99%电流组 (n={len(heights_99)}):")
    print(f"  平均值: {stats_99['mean']:.3f} mm")
    print(f"  标准差: {stats_99['std']:.3f} mm")
    print(f"  范围: {stats_99['min']:.3f} - {stats_99['max']:.3f} mm")
    
    in_range_99 = sum(1 for h in heights_99 if 2.1 <= h <= 2.4)
    compliance_99 = (in_range_99 / len(heights_99)) * 100 if len(heights_99) > 0 else 0
    print(f"  目标范围符合率: {in_range_99}/{len(heights_99)} ({compliance_99:.1f}%)")
    
    # 高度差异分析
    if len(heights_97) > 0 and len(heights_99) > 0:
        height_diff = stats_99['mean'] - stats_97['mean']
        print(f"\n=== 两组高度差异分析 ===")
        print(f"平均高度差异: {height_diff:.3f} mm")
        print(f"差异方向: {'99%组更高' if height_diff > 0 else '97%组更高' if height_diff < 0 else '基本相同'}")
    
    return {
        'heights_97_mean': stats_97['mean'],
        'heights_97_std': stats_97['std'],
        'heights_99_mean': stats_99['mean'],
        'heights_99_std': stats_99['std'],
        'compliance_97': compliance_97,
        'compliance_99': compliance_99
    }

def analyze_process_parameters(data):
    """工艺参数分析"""
    print("\n" + "="*60)
    print("工艺参数分析")
    print("="*60)
    
    # 提取关键参数
    def extract_param(data, param_name):
        values = []
        for row in data:
            try:
                value = float(row[param_name])
                if value > 0:
                    values.append(value)
            except (ValueError, TypeError, KeyError):
                continue
        return values
    
    key_params = {
        'gas_fill_pressure': '充气压力',
        'electrode_pressure': '电极压力', 
        'room_temperature': '环境温度',
        's2_percent_current': 'S2电流',
        's2_hold': 'S2保持时间'
    }
    
    for param_key, param_name in key_params.items():
        values = extract_param(data, param_key)
        if values:
            mean_val = sum(values) / len(values)
            min_val = min(values)
            max_val = max(values)
            print(f"\n{param_name} ({param_key}):")
            print(f"  样本数: {len(values)}")
            print(f"  平均值: {mean_val:.2f}")
            print(f"  范围: {min_val:.2f} - {max_val:.2f}")

def generate_stage1_report(h1_results, height_results):
    """生成第一阶段分析报告"""
    print("\n" + "="*80)
    print("DOE v7.1 第一阶段(H1假设验证)统计分析报告")
    print("="*80)
    
    print(f"\n📊 **实验设计执行情况**")
    print(f"- 计划样本数: 30 (每组15个)")
    print(f"- 实际样本数: {h1_results['group_97_n'] + h1_results['group_99_n']}")
    print(f"- 97%电流组: {h1_results['group_97_n']}个样本")
    print(f"- 99%电流组: {h1_results['group_99_n']}个样本")
    print(f"- 数据完整性: {'✅ 符合设计要求' if h1_results['group_97_n'] == 15 and h1_results['group_99_n'] == 15 else '⚠️ 样本数不符合设计'}")
    
    print(f"\n🎯 **H1假设验证结果**")
    print(f"假设内容: 99%S1电流相比97%电流会显著增加Type 8裂纹失效率")
    print(f"- 97%电流组Type 8失效率: {h1_results['type8_rate_97']:.1f}%")
    print(f"- 99%电流组Type 8失效率: {h1_results['type8_rate_99']:.1f}%")
    print(f"- Type 8失效率差异: {h1_results['type8_rate_99'] - h1_results['type8_rate_97']:.1f}个百分点")
    
    # 判断假设支持情况
    if h1_results['type8_rate_99'] > h1_results['type8_rate_97']:
        conclusion = "✅ 数据趋势支持H1假设"
        recommendation = "建议在第二阶段采用97%S1电流以降低裂纹风险"
    elif h1_results['type8_rate_97'] > h1_results['type8_rate_99']:
        conclusion = "❌ 数据趋势与H1假设相反"
        recommendation = "97%电流组裂纹失效率更高，建议重新评估假设"
    else:
        conclusion = "➖ 两组Type 8失效率相同"
        recommendation = "两种电流水平的裂纹风险无差异，可根据其他因素选择"
    
    print(f"- **结论**: {conclusion}")
    print(f"- **建议**: {recommendation}")
    
    print(f"\n📏 **焊接高度分析结果**")
    print(f"- 97%电流组平均高度: {height_results['heights_97_mean']:.3f}±{height_results['heights_97_std']:.3f} mm")
    print(f"- 99%电流组平均高度: {height_results['heights_99_mean']:.3f}±{height_results['heights_99_std']:.3f} mm")
    print(f"- 97%电流组目标范围符合率: {height_results['compliance_97']:.1f}%")
    print(f"- 99%电流组目标范围符合率: {height_results['compliance_99']:.1f}%")
    
    height_diff = height_results['heights_99_mean'] - height_results['heights_97_mean']
    print(f"- 平均高度差异: {height_diff:.3f} mm ({'99%组更高' if height_diff > 0 else '97%组更高' if height_diff < 0 else '基本相同'})")
    
    print(f"\n📈 **整体质量表现**")
    print(f"- 97%电流组成功率: {h1_results['success_rate_97']:.1f}%")
    print(f"- 99%电流组成功率: {h1_results['success_rate_99']:.1f}%")
    print(f"- 97%电流组Type 4失效率: {h1_results['type4_rate_97']:.1f}%")
    print(f"- 99%电流组Type 4失效率: {h1_results['type4_rate_99']:.1f}%")
    
    # 整体成功率
    total_success_rate = (h1_results['success_rate_97'] + h1_results['success_rate_99']) / 2
    
    print(f"\n🚀 **第二阶段执行建议**")
    
    if total_success_rate > 50:
        stage2_recommendation = "✅ 建议继续执行第二阶段CCD建模"
        print(f"- **执行决策**: {stage2_recommendation}")
        print(f"- **理由**: 整体成功率{total_success_rate:.1f}%，具备进一步优化价值")
    else:
        stage2_recommendation = "⚠️ 建议暂停第二阶段，优先解决基础工艺问题"
        print(f"- **执行决策**: {stage2_recommendation}")
        print(f"- **理由**: 整体成功率{total_success_rate:.1f}%过低，需要基础工艺改进")
    
    # 关键发现总结
    print(f"\n🔍 **关键发现总结**")
    
    if h1_results['type8_rate_97'] == 0 and h1_results['type8_rate_99'] == 0:
        print(f"1. 两组均无Type 8裂纹失效，H1假设无法验证")
    else:
        print(f"1. Type 8裂纹失效: 97%组{h1_results['type8_rate_97']:.1f}% vs 99%组{h1_results['type8_rate_99']:.1f}%")
    
    print(f"2. Type 4高度失效: 97%组{h1_results['type4_rate_97']:.1f}% vs 99%组{h1_results['type4_rate_99']:.1f}%")
    print(f"3. 整体成功率: 97%组{h1_results['success_rate_97']:.1f}% vs 99%组{h1_results['success_rate_99']:.1f}%")
    print(f"4. 焊接高度控制: 97%组符合率{height_results['compliance_97']:.1f}% vs 99%组符合率{height_results['compliance_99']:.1f}%")
    
    return {
        'stage2_recommendation': stage2_recommendation,
        'total_success_rate': total_success_rate,
        'conclusion': conclusion
    }

def main():
    """主分析函数"""
    print("G3P OP60 DOE v7.1 第一阶段(H1假设验证)统计分析")
    print("分析时间: 2025年8月2日")
    print("="*80)
    
    # 1. 加载数据
    stage1_data = load_stage1_data()
    
    # 2. H1假设验证分析
    h1_results = analyze_h1_hypothesis(stage1_data)
    
    # 3. 焊接高度分析
    height_results = analyze_weld_height(h1_results)
    
    # 4. 工艺参数分析
    analyze_process_parameters(stage1_data)
    
    # 5. 生成综合报告
    final_results = generate_stage1_report(h1_results, height_results)
    
    print(f"\n" + "="*80)
    print("分析完成！")
    print("="*80)
    
    return stage1_data, h1_results, height_results, final_results

if __name__ == "__main__":
    stage1_data, h1_results, height_results, final_results = main()
