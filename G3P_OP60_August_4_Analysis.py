#!/usr/bin/env python3
"""
G3P OP60 电阻焊 8月4日数据统计分析
分析8月4日最新数据，评估测试路径、质量表现和参数效应
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_clean_data():
    """加载并清理数据"""
    print("=== 数据加载与清理 ===")
    
    # 读取数据
    df = pd.read_csv('02_Data_And_Analysis/Raw_Data/Raw_Data_2025-08-04_16-20.csv')
    
    # 清理数据
    df = df.dropna(axis=1, how='all')
    df = df.loc[:, ~df.columns.str.contains('^Unnamed')]
    df_valid = df[df['verification'] == 'Rocky'].copy()
    
    print(f"总样本数: {len(df_valid)}")
    
    # 解析日期
    df_valid['datetime'] = pd.to_datetime(df_valid['date_time'], format='%Y/%m/%d', errors='coerce')
    
    return df_valid

def analyze_august_4_data(df_valid):
    """分析8月4日数据"""
    print("\n=== 8月4日数据分析 ===")
    
    # 筛选8月4日数据
    august_4_data = df_valid[df_valid['datetime'].dt.date == pd.Timestamp('2025-08-04').date()].copy()
    
    if len(august_4_data) == 0:
        print("未找到8月4日数据")
        return None
    
    print(f"8月4日样本数: {len(august_4_data)}")
    print(f"时间范围: {august_4_data['datetime'].min()} 到 {august_4_data['datetime'].max()}")
    
    return august_4_data

def analyze_test_paths(august_4_data):
    """分析测试路径"""
    print("\n=== 测试路径分析 ===")
    
    # 检查推出测试数据
    pushout_samples = august_4_data[august_4_data['push_out_force'].notna()].copy()
    print(f"进行推出测试的样品数: {len(pushout_samples)}")
    
    # 检查金相分析数据 (有完整焊缝宽度和间隙数据)
    metallography_samples = august_4_data[
        (august_4_data['weld_width_a'].notna()) & 
        (august_4_data['weld_width_b'].notna()) & 
        (august_4_data['disk_gap_a'].notna()) & 
        (august_4_data['disk_gap_b'].notna()) &
        (august_4_data['weld_width_a'] > 0) &
        (august_4_data['weld_width_b'] > 0)
    ].copy()
    print(f"进行金相分析的样品数: {len(metallography_samples)}")
    
    # 检查数据重叠
    overlap = set(pushout_samples.index) & set(metallography_samples.index)
    print(f"同时进行两种测试的样品数: {len(overlap)}")
    
    # 只有外观检查的样品
    appearance_only = august_4_data[
        (august_4_data['push_out_force'].isna()) &
        ((august_4_data['weld_width_a'].isna()) | (august_4_data['weld_width_a'] == 0))
    ].copy()
    print(f"仅进行外观检查的样品数: {len(appearance_only)}")
    
    return {
        'pushout': pushout_samples,
        'metallography': metallography_samples,
        'appearance_only': appearance_only,
        'total': august_4_data
    }

def analyze_quality_performance(test_paths):
    """分析质量表现"""
    print("\n=== 质量表现统计 ===")
    
    august_4_data = test_paths['total']
    
    # 总体成功率
    success_rate = (august_4_data['weld_failure_type'] == 1.0).mean() * 100
    print(f"总体成功率: {success_rate:.1f}%")
    
    # 失效类型分布
    failure_dist = august_4_data['weld_failure_type'].value_counts().sort_index()
    print("\n失效类型分布:")
    failure_names = {
        1.0: 'Type 1 (成功)', 
        4.0: 'Type 4 (高度失效)', 
        8.0: 'Type 8 (裂纹失效)', 
        5.0: 'Type 5 (宽度失效)', 
        6.0: 'Type 6 (脱落失效)'
    }
    
    for failure_type, count in failure_dist.items():
        percentage = count / len(august_4_data) * 100
        failure_name = failure_names.get(failure_type, f'Type {failure_type}')
        print(f"  {failure_name}: {count}个 ({percentage:.1f}%)")
    
    # 泄漏测试结果
    leakage_dist = august_4_data['leakage'].value_counts()
    print(f"\n泄漏测试:")
    for result, count in leakage_dist.items():
        percentage = count / len(august_4_data) * 100
        print(f"  {result}: {count}个 ({percentage:.1f}%)")
    
    return {
        'success_rate': success_rate,
        'failure_distribution': failure_dist,
        'leakage_distribution': leakage_dist
    }

def analyze_parameter_effects(august_4_data):
    """分析参数效应"""
    print("\n=== 参数效应分析 ===")
    
    # 成功样品
    success_samples = august_4_data[august_4_data['weld_failure_type'] == 1.0].copy()
    
    if len(success_samples) == 0:
        print("没有成功样品进行参数分析")
        return None
    
    print(f"成功样品数: {len(success_samples)}")
    
    # 关键参数统计
    key_params = ['electrode_pressure', 's1_percent_current', 's1_hold', 's1_squeeze']
    
    print("\n成功样品关键参数统计:")
    for param in key_params:
        if param in success_samples.columns:
            values = success_samples[param].dropna()
            if len(values) > 0:
                print(f"  {param}: 均值={values.mean():.1f}, 标准差={values.std():.2f}, 范围=[{values.min():.1f}, {values.max():.1f}]")
    
    return success_samples

def compare_with_previous_data(df_valid, august_4_data):
    """与之前数据对比"""
    print("\n=== 与之前数据对比 ===")
    
    # 8月1-3日数据
    august_1_3_data = df_valid[
        (df_valid['datetime'].dt.date >= pd.Timestamp('2025-08-01').date()) &
        (df_valid['datetime'].dt.date <= pd.Timestamp('2025-08-03').date())
    ].copy()
    
    print(f"8月1-3日样本数: {len(august_1_3_data)}")
    print(f"8月4日样本数: {len(august_4_data)}")
    
    if len(august_1_3_data) > 0:
        # 成功率对比
        success_rate_1_3 = (august_1_3_data['weld_failure_type'] == 1.0).mean() * 100
        success_rate_4 = (august_4_data['weld_failure_type'] == 1.0).mean() * 100
        
        print(f"\n成功率对比:")
        print(f"  8月1-3日: {success_rate_1_3:.1f}%")
        print(f"  8月4日: {success_rate_4:.1f}%")
        print(f"  变化: {success_rate_4 - success_rate_1_3:+.1f}%")
        
        # 失效模式对比
        print(f"\n失效模式对比:")
        failure_1_3 = august_1_3_data['weld_failure_type'].value_counts(normalize=True) * 100
        failure_4 = august_4_data['weld_failure_type'].value_counts(normalize=True) * 100
        
        for failure_type in [1.0, 4.0, 8.0]:
            rate_1_3 = failure_1_3.get(failure_type, 0)
            rate_4 = failure_4.get(failure_type, 0)
            change = rate_4 - rate_1_3
            failure_name = {1.0: '成功', 4.0: '高度失效', 8.0: '裂纹失效'}.get(failure_type, f'Type {failure_type}')
            print(f"  {failure_name}: 8月1-3日={rate_1_3:.1f}%, 8月4日={rate_4:.1f}%, 变化={change:+.1f}%")
    
    return august_1_3_data

def generate_visualizations(august_4_data, test_paths):
    """生成可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('G3P OP60 8月4日数据分析', fontsize=16, fontweight='bold')
    
    # 1. 失效类型分布
    failure_dist = august_4_data['weld_failure_type'].value_counts().sort_index()
    failure_names = {1.0: '成功', 4.0: '高度失效', 8.0: '裂纹失效', 5.0: '宽度失效', 6.0: '脱落失效'}
    labels = [failure_names.get(x, f'Type {x}') for x in failure_dist.index]
    
    axes[0,0].pie(failure_dist.values, labels=labels, autopct='%1.1f%%', startangle=90)
    axes[0,0].set_title('失效类型分布')
    
    # 2. 测试路径分布
    test_path_counts = [
        len(test_paths['metallography']),
        len(test_paths['pushout']),
        len(test_paths['appearance_only'])
    ]
    test_path_labels = ['金相分析', '推出测试', '仅外观检查']
    
    axes[0,1].bar(test_path_labels, test_path_counts, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[0,1].set_title('测试路径分布')
    axes[0,1].set_ylabel('样品数量')
    
    # 3. 成功样品的电极压力分布
    success_samples = august_4_data[august_4_data['weld_failure_type'] == 1.0]
    if len(success_samples) > 0:
        axes[1,0].hist(success_samples['electrode_pressure'].dropna(), bins=10, alpha=0.7, color='green')
        axes[1,0].set_title('成功样品电极压力分布')
        axes[1,0].set_xlabel('电极压力')
        axes[1,0].set_ylabel('频次')
    
    # 4. 成功样品的S1电流分布
    if len(success_samples) > 0:
        axes[1,1].hist(success_samples['s1_percent_current'].dropna(), bins=10, alpha=0.7, color='blue')
        axes[1,1].set_title('成功样品S1电流分布')
        axes[1,1].set_xlabel('S1电流百分比')
        axes[1,1].set_ylabel('频次')
    
    plt.tight_layout()
    plt.savefig('G3P_OP60_August_4_Analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存为: G3P_OP60_August_4_Analysis.png")
    
    return fig

def main():
    """主函数"""
    print("G3P OP60 电阻焊 8月4日数据统计分析")
    print("=" * 50)
    
    # 1. 加载数据
    df_valid = load_and_clean_data()
    
    # 2. 分析8月4日数据
    august_4_data = analyze_august_4_data(df_valid)
    if august_4_data is None:
        return
    
    # 3. 测试路径分析
    test_paths = analyze_test_paths(august_4_data)
    
    # 4. 质量表现分析
    quality_stats = analyze_quality_performance(test_paths)
    
    # 5. 参数效应分析
    success_samples = analyze_parameter_effects(august_4_data)
    
    # 6. 与之前数据对比
    august_1_3_data = compare_with_previous_data(df_valid, august_4_data)
    
    # 7. 生成可视化
    fig = generate_visualizations(august_4_data, test_paths)
    
    # 8. 生成总结报告
    print("\n" + "=" * 50)
    print("分析总结:")
    print(f"1. 8月4日共测试 {len(august_4_data)} 个样品")
    print(f"2. 总体成功率: {quality_stats['success_rate']:.1f}%")
    print(f"3. 金相分析样品: {len(test_paths['metallography'])} 个")
    print(f"4. 推出测试样品: {len(test_paths['pushout'])} 个")
    
    if success_samples is not None and len(success_samples) > 0:
        print(f"5. 成功样品关键参数范围:")
        print(f"   - 电极压力: {success_samples['electrode_pressure'].min():.0f}-{success_samples['electrode_pressure'].max():.0f}")
        print(f"   - S1电流: {success_samples['s1_percent_current'].min():.0f}-{success_samples['s1_percent_current'].max():.0f}%")
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
