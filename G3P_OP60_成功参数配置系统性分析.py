#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 电阻焊接成功参数配置系统性分析
基于244个样品，跨越9个月的全面分析
识别和表征成功的参数配置
"""

import csv
import json
from collections import defaultdict, Counter
from datetime import datetime
import math

class G3PParameterAnalysis:
    def __init__(self):
        self.data = []
        self.qualified_samples = []
        self.similar_samples = []
        self.consolidated_dataset = []
        
    def load_data(self, filename='02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv'):
        """加载数据"""
        try:
            with open(filename, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    self.data.append(row)
            print(f"成功加载数据，共{len(self.data)}条记录")
            return True
        except Exception as e:
            print(f"加载数据时出错: {e}")
            return False
    
    def phase1_identify_qualified_results(self):
        """阶段1：识别合格的测试结果"""
        print("\n" + "="*80)
        print("阶段1：识别合格的测试结果")
        print("="*80)
        
        qualified_count = 0
        
        for sample in self.data:
            is_qualified = False
            qualification_reason = []
            
            # 检查推出力测试
            if sample.get('push_out_force') and sample['push_out_force'] != '-':
                try:
                    push_force = float(sample['push_out_force'])
                    if push_force >= 10.0:  # 假设接受标准为10N
                        is_qualified = True
                        qualification_reason.append(f"推出力测试通过({push_force}N)")
                except ValueError:
                    pass
            
            # 检查压力循环测试
            if sample.get('leakage') == 'Pass':
                is_qualified = True
                qualification_reason.append("压力循环测试通过")
            
            # 检查压力测试循环数
            if sample.get('pressure_test_cycles') and sample['pressure_test_cycles'] != '-':
                try:
                    cycles = int(sample['pressure_test_cycles'])
                    if cycles >= 4500:  # 假设接受标准为4500次循环
                        is_qualified = True
                        qualification_reason.append(f"压力测试循环通过({cycles}次)")
                except ValueError:
                    pass
            
            if is_qualified:
                self.qualified_samples.append({
                    'sample': sample,
                    'reasons': qualification_reason,
                    'test_pathway': self._determine_test_pathway(sample)
                })
                qualified_count += 1
        
        print(f"识别出{qualified_count}个合格样品")
        
        # 统计合格原因
        qualification_reasons = []
        for qs in self.qualified_samples:
            qualification_reasons.extend(qs['reasons'])
        
        reason_counts = Counter(qualification_reasons)
        print("\n合格原因统计:")
        for reason, count in reason_counts.most_common():
            print(f"  {reason}: {count}次")
        
        return qualified_count
    
    def _determine_test_pathway(self, sample):
        """确定测试路径"""
        if sample.get('push_out_force') and sample['push_out_force'] != '-':
            return "推出力测试"
        elif sample.get('weld_width_a') and sample['weld_width_a'] != '-':
            return "金相分析"
        else:
            return "未知"
    
    def phase2_parameter_pattern_expansion(self):
        """阶段2：参数模式扩展"""
        print("\n" + "="*80)
        print("阶段2：参数模式扩展")
        print("="*80)
        
        if not self.qualified_samples:
            print("没有合格样品，无法进行参数模式扩展")
            return
        
        # 定义容差范围
        tolerances = {
            'electrode_pressure': 0.5,  # ±0.5 psi
            's1_percent_current': 1.0,  # ±1%
            's2_percent_current': 1.0,  # ±1%
            's1_hold': 0.5,  # ±0.5 cycles
            's2_hold': 0.5,  # ±0.5 cycles
        }
        
        # 为每个合格样品找到相似参数配置
        for qualified in self.qualified_samples:
            qualified_params = self._extract_key_parameters(qualified['sample'])
            similar_samples = []
            
            for sample in self.data:
                if sample == qualified['sample']:
                    continue
                
                sample_params = self._extract_key_parameters(sample)
                if self._is_similar_configuration(qualified_params, sample_params, tolerances):
                    similar_samples.append(sample)
            
            self.similar_samples.extend(similar_samples)
        
        # 去重
        unique_similar = []
        seen_ids = set()
        for sample in self.similar_samples:
            sample_id = sample.get('sample_id', '')
            if sample_id not in seen_ids:
                unique_similar.append(sample)
                seen_ids.add(sample_id)
        
        self.similar_samples = unique_similar
        print(f"找到{len(self.similar_samples)}个相似参数配置的样品")
        
        # 统计参数相似性
        self._analyze_parameter_similarity()
    
    def _extract_key_parameters(self, sample):
        """提取关键参数"""
        params = {}
        try:
            params['electrode_pressure'] = float(sample.get('electrode_pressure', 0))
            params['s1_percent_current'] = float(sample.get('s1_percent_current', 0))
            params['s2_percent_current'] = float(sample.get('s2_percent_current', 0))
            params['s1_hold'] = float(sample.get('s1_hold', 0))
            params['s2_hold'] = float(sample.get('s2_hold', 0))
            params['s1_cool'] = float(sample.get('s1_cool', 0))
            params['s2_cool'] = float(sample.get('s2_cool', 0))
        except (ValueError, TypeError):
            pass
        return params
    
    def _is_similar_configuration(self, params1, params2, tolerances):
        """判断参数配置是否相似"""
        for param, tolerance in tolerances.items():
            if param in params1 and param in params2:
                diff = abs(params1[param] - params2[param])
                if diff > tolerance:
                    return False
        return True
    
    def _analyze_parameter_similarity(self):
        """分析参数相似性"""
        print("\n参数相似性分析:")
        
        # 统计电极压力分布
        pressures = []
        for sample in self.similar_samples:
            try:
                pressure = float(sample.get('electrode_pressure', 0))
                if pressure > 0:
                    pressures.append(pressure)
            except ValueError:
                continue
        
        if pressures:
            print(f"  电极压力范围: {min(pressures):.1f} - {max(pressures):.1f} psi")
            print(f"  平均电极压力: {sum(pressures)/len(pressures):.1f} psi")
    
    def phase3_metallographic_verification(self):
        """阶段3：金相结果验证"""
        print("\n" + "="*80)
        print("阶段3：金相结果验证")
        print("="*80)
        
        all_target_samples = self.qualified_samples + [{'sample': s} for s in self.similar_samples]
        
        complete_metallographic = []
        incomplete_metallographic = []
        missing_metallographic = []
        
        for item in all_target_samples:
            sample = item['sample']
            
            # 检查金相数据完整性
            weld_width_a = sample.get('weld_width_a', '')
            weld_width_b = sample.get('weld_width_b', '')
            gap_a = sample.get('disk_gap_a', '')
            gap_b = sample.get('disk_gap_b', '')
            height = sample.get('post_weld_disk_holder_height', '')
            
            metallographic_data = [weld_width_a, weld_width_b, gap_a, gap_b, height]
            valid_data = [d for d in metallographic_data if d and d != '-']
            
            if len(valid_data) >= 4:  # 至少4个参数有数据
                complete_metallographic.append(sample)
            elif len(valid_data) >= 2:  # 2-3个参数有数据
                incomplete_metallographic.append(sample)
            else:
                missing_metallographic.append(sample)
        
        print(f"完整金相数据: {len(complete_metallographic)}个样品")
        print(f"部分金相数据: {len(incomplete_metallographic)}个样品")
        print(f"缺失金相数据: {len(missing_metallographic)}个样品")
        
        # 分析金相数据
        if complete_metallographic:
            self._analyze_metallographic_data(complete_metallographic)
        
        return complete_metallographic, incomplete_metallographic, missing_metallographic
    
    def _analyze_metallographic_data(self, samples):
        """分析金相数据"""
        print("\n金相数据分析:")
        
        weld_widths_a = []
        weld_widths_b = []
        gaps_a = []
        gaps_b = []
        heights = []
        
        for sample in samples:
            try:
                if sample.get('weld_width_a') and sample['weld_width_a'] != '-':
                    weld_widths_a.append(float(sample['weld_width_a']))
                if sample.get('weld_width_b') and sample['weld_width_b'] != '-':
                    weld_widths_b.append(float(sample['weld_width_b']))
                if sample.get('disk_gap_a') and sample['disk_gap_a'] != '-':
                    gaps_a.append(float(sample['disk_gap_a']))
                if sample.get('disk_gap_b') and sample['disk_gap_b'] != '-':
                    gaps_b.append(float(sample['disk_gap_b']))
                if sample.get('post_weld_disk_holder_height') and sample['post_weld_disk_holder_height'] != '-':
                    heights.append(float(sample['post_weld_disk_holder_height']))
            except ValueError:
                continue
        
        if weld_widths_a:
            print(f"  焊接宽度A侧: {min(weld_widths_a):.3f} - {max(weld_widths_a):.3f} mm")
        if weld_widths_b:
            print(f"  焊接宽度B侧: {min(weld_widths_b):.3f} - {max(weld_widths_b):.3f} mm")
        if gaps_a:
            print(f"  间隙A侧: {min(gaps_a):.3f} - {max(gaps_a):.3f} mm")
        if gaps_b:
            print(f"  间隙B侧: {min(gaps_b):.3f} - {max(gaps_b):.3f} mm")
        if heights:
            print(f"  焊接后高度: {min(heights):.3f} - {max(heights):.3f} mm")
    
    def phase4_comprehensive_dataset_analysis(self):
        """阶段4：综合数据集分析"""
        print("\n" + "="*80)
        print("阶段4：综合数据集分析")
        print("="*80)
        
        # 合并所有相关样品
        all_samples = []
        for qs in self.qualified_samples:
            all_samples.append(qs['sample'])
        all_samples.extend(self.similar_samples)
        
        # 去重
        unique_samples = []
        seen_ids = set()
        for sample in all_samples:
            sample_id = sample.get('sample_id', '')
            if sample_id not in seen_ids:
                unique_samples.append(sample)
                seen_ids.add(sample_id)
        
        self.consolidated_dataset = unique_samples
        print(f"综合数据集包含{len(self.consolidated_dataset)}个样品")
        
        # 统计分析
        self._statistical_analysis()
        self._success_rate_analysis()
    
    def _statistical_analysis(self):
        """统计分析"""
        print("\nDOE参数统计分析:")
        
        # 电极压力分析
        pressures = []
        s1_currents = []
        s2_currents = []
        s1_holds = []
        s2_holds = []
        
        for sample in self.consolidated_dataset:
            try:
                if sample.get('electrode_pressure'):
                    pressures.append(float(sample['electrode_pressure']))
                if sample.get('s1_percent_current'):
                    s1_currents.append(float(sample['s1_percent_current']))
                if sample.get('s2_percent_current'):
                    s2_currents.append(float(sample['s2_percent_current']))
                if sample.get('s1_hold'):
                    s1_holds.append(float(sample['s1_hold']))
                if sample.get('s2_hold'):
                    s2_holds.append(float(sample['s2_hold']))
            except ValueError:
                continue
        
        if pressures:
            print(f"  电极压力: {min(pressures):.1f} - {max(pressures):.1f} psi (平均: {sum(pressures)/len(pressures):.1f})")
        if s1_currents:
            print(f"  S1电流: {min(s1_currents):.1f} - {max(s1_currents):.1f}% (平均: {sum(s1_currents)/len(s1_currents):.1f})")
        if s2_currents:
            print(f"  S2电流: {min(s2_currents):.1f} - {max(s2_currents):.1f}% (平均: {sum(s2_currents)/len(s2_currents):.1f})")
        if s1_holds:
            print(f"  S1保持时间: {min(s1_holds):.1f} - {max(s1_holds):.1f} cycles (平均: {sum(s1_holds)/len(s1_holds):.1f})")
        if s2_holds:
            print(f"  S2保持时间: {min(s2_holds):.1f} - {max(s2_holds):.1f} cycles (平均: {sum(s2_holds)/len(s2_holds):.1f})")
    
    def _success_rate_analysis(self):
        """成功率分析"""
        print("\n成功率分析:")
        
        # 按参数组合分组
        parameter_groups = defaultdict(list)
        
        for sample in self.consolidated_dataset:
            try:
                pressure = float(sample.get('electrode_pressure', 0))
                s1_current = float(sample.get('s1_percent_current', 0))
                s2_current = float(sample.get('s2_percent_current', 0))
                
                # 创建参数组合键
                key = f"P{pressure:.0f}_S1{s1_current:.0f}_S2{s2_current:.0f}"
                parameter_groups[key].append(sample)
            except ValueError:
                continue
        
        print(f"  参数组合数量: {len(parameter_groups)}")
        
        # 计算每个组合的成功率
        for key, samples in parameter_groups.items():
            success_count = 0
            for sample in samples:
                if sample.get('leakage') == 'Pass':
                    success_count += 1
            
            success_rate = (success_count / len(samples)) * 100
            print(f"  {key}: {len(samples)}个样品, 成功率{success_rate:.1f}%")
    
    def phase5_pattern_recognition(self):
        """阶段5：模式识别和洞察"""
        print("\n" + "="*80)
        print("阶段5：模式识别和洞察")
        print("="*80)
        
        # 分析成功参数配置的共同点
        self._analyze_success_commonalities()
        
        # 分析参数交互
        self._analyze_parameter_interactions()
        
        # 分析DOE阶段演变
        self._analyze_doe_evolution()
    
    def _analyze_success_commonalities(self):
        """分析成功配置的共同点"""
        print("\n成功参数配置共同点分析:")
        
        successful_samples = [s for s in self.consolidated_dataset if s.get('leakage') == 'Pass']
        
        if not successful_samples:
            print("  没有找到成功的样品")
            return
        
        # 分析电极压力
        pressures = []
        for sample in successful_samples:
            try:
                pressure = float(sample.get('electrode_pressure', 0))
                if pressure > 0:
                    pressures.append(pressure)
            except ValueError:
                continue
        
        if pressures:
            print(f"  成功配置电极压力范围: {min(pressures):.1f} - {max(pressures):.1f} psi")
            print(f"  最常见电极压力: {Counter(pressures).most_common(1)[0][0]:.1f} psi")
        
        # 分析电流设置
        s1_currents = []
        s2_currents = []
        for sample in successful_samples:
            try:
                s1_current = float(sample.get('s1_percent_current', 0))
                s2_current = float(sample.get('s2_percent_current', 0))
                if s1_current > 0:
                    s1_currents.append(s1_current)
                if s2_current > 0:
                    s2_currents.append(s2_current)
            except ValueError:
                continue
        
        if s1_currents:
            print(f"  成功配置S1电流范围: {min(s1_currents):.1f} - {max(s1_currents):.1f}%")
        if s2_currents:
            print(f"  成功配置S2电流范围: {min(s2_currents):.1f} - {max(s2_currents):.1f}%")
    
    def _analyze_parameter_interactions(self):
        """分析参数交互"""
        print("\n参数交互分析:")
        
        # 分析电极压力与电流的关系
        pressure_current_pairs = []
        for sample in self.consolidated_dataset:
            try:
                pressure = float(sample.get('electrode_pressure', 0))
                s1_current = float(sample.get('s1_percent_current', 0))
                if pressure > 0 and s1_current > 0:
                    pressure_current_pairs.append((pressure, s1_current))
            except ValueError:
                continue
        
        if pressure_current_pairs:
            # 计算相关系数
            pressures, currents = zip(*pressure_current_pairs)
            correlation = self._calculate_correlation(pressures, currents)
            print(f"  电极压力与S1电流相关系数: {correlation:.3f}")
    
    def _analyze_doe_evolution(self):
        """分析DOE阶段演变"""
        print("\nDOE阶段演变分析:")
        
        # 按日期分组
        date_groups = defaultdict(list)
        for sample in self.consolidated_dataset:
            date = sample.get('date_time', '')
            if date:
                date_groups[date].append(sample)
        
        print(f"  测试时间跨度: {len(date_groups)}个测试日期")
        
        # 分析参数演变趋势
        for date in sorted(date_groups.keys()):
            samples = date_groups[date]
            pressures = []
            for sample in samples:
                try:
                    pressure = float(sample.get('electrode_pressure', 0))
                    if pressure > 0:
                        pressures.append(pressure)
                except ValueError:
                    continue
            
            if pressures:
                avg_pressure = sum(pressures) / len(pressures)
                print(f"  {date}: 平均电极压力 {avg_pressure:.1f} psi")
    
    def _calculate_correlation(self, x, y):
        """计算相关系数"""
        if len(x) != len(y) or len(x) < 2:
            return 0
        
        n = len(x)
        sum_x = sum(x)
        sum_y = sum(y)
        sum_xy = sum(x[i] * y[i] for i in range(n))
        sum_x2 = sum(x[i] ** 2 for i in range(n))
        sum_y2 = sum(y[i] ** 2 for i in range(n))
        
        numerator = n * sum_xy - sum_x * sum_y
        denominator = math.sqrt((n * sum_x2 - sum_x ** 2) * (n * sum_y2 - sum_y ** 2))
        
        if denominator == 0:
            return 0
        
        return numerator / denominator
    
    def generate_report(self):
        """生成分析报告"""
        print("\n" + "="*80)
        print("生成分析报告")
        print("="*80)
        
        report = {
            "分析时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "数据概况": {
                "总样品数": len(self.data),
                "合格样品数": len(self.qualified_samples),
                "相似参数样品数": len(self.similar_samples),
                "综合数据集大小": len(self.consolidated_dataset)
            },
            "阶段1结果": {
                "合格样品识别": len(self.qualified_samples),
                "测试路径分布": Counter([qs['test_pathway'] for qs in self.qualified_samples])
            },
            "阶段2结果": {
                "相似参数样品": len(self.similar_samples)
            },
            "阶段3结果": {
                "完整金相数据": len([s for s in self.consolidated_dataset if self._has_complete_metallographic(s)]),
                "部分金相数据": len([s for s in self.consolidated_dataset if self._has_partial_metallographic(s)]),
                "缺失金相数据": len([s for s in self.consolidated_dataset if not self._has_any_metallographic(s)])
            },
            "阶段4结果": {
                "综合数据集": len(self.consolidated_dataset),
                "成功率": self._calculate_overall_success_rate()
            }
        }
        
        # 保存报告
        with open('G3P_OP60_成功参数配置分析报告.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("分析报告已保存为: G3P_OP60_成功参数配置分析报告.json")
        
        return report
    
    def _has_complete_metallographic(self, sample):
        """检查是否有完整的金相数据"""
        metallographic_fields = ['weld_width_a', 'weld_width_b', 'disk_gap_a', 'disk_gap_b', 'post_weld_disk_holder_height']
        valid_count = sum(1 for field in metallographic_fields if sample.get(field) and sample[field] != '-')
        return valid_count >= 4
    
    def _has_partial_metallographic(self, sample):
        """检查是否有部分金相数据"""
        metallographic_fields = ['weld_width_a', 'weld_width_b', 'disk_gap_a', 'disk_gap_b', 'post_weld_disk_holder_height']
        valid_count = sum(1 for field in metallographic_fields if sample.get(field) and sample[field] != '-')
        return 2 <= valid_count < 4
    
    def _has_any_metallographic(self, sample):
        """检查是否有任何金相数据"""
        metallographic_fields = ['weld_width_a', 'weld_width_b', 'disk_gap_a', 'disk_gap_b', 'post_weld_disk_holder_height']
        return any(sample.get(field) and sample[field] != '-' for field in metallographic_fields)
    
    def _calculate_overall_success_rate(self):
        """计算整体成功率"""
        if not self.consolidated_dataset:
            return 0
        
        success_count = sum(1 for sample in self.consolidated_dataset if sample.get('leakage') == 'Pass')
        return (success_count / len(self.consolidated_dataset)) * 100

def main():
    """主函数"""
    print("G3P OP60 电阻焊接成功参数配置系统性分析")
    print("="*80)
    
    # 创建分析器
    analyzer = G3PParameterAnalysis()
    
    # 加载数据
    if not analyzer.load_data():
        print("数据加载失败，退出分析")
        return
    
    # 执行五个阶段的分析
    analyzer.phase1_identify_qualified_results()
    analyzer.phase2_parameter_pattern_expansion()
    analyzer.phase3_metallographic_verification()
    analyzer.phase4_comprehensive_dataset_analysis()
    analyzer.phase5_pattern_recognition()
    
    # 生成报告
    report = analyzer.generate_report()
    
    print("\n" + "="*80)
    print("分析完成")
    print("="*80)

if __name__ == "__main__":
    main() 