# G3P OP60项目 - iCloud Drive兼容文件移动脚本
# 日期: 2025-08-03
# 目的: 解决iCloud Drive同步干扰和文件冲突问题

Write-Host "=== G3P OP60 iCloud兼容文件移动脚本 ===" -ForegroundColor Green
Write-Host "日期: 2025-08-03" -ForegroundColor Yellow
Write-Host "专为iCloud Drive环境设计..." -ForegroundColor Yellow

# 设置错误处理
$ErrorActionPreference = "Continue"

# 检查是否在iCloud环境
$currentPath = $PWD.Path
$isICloudEnvironment = $currentPath -like "*iCloudDrive*"

if ($isICloudEnvironment) {
    Write-Host "🔄 检测到iCloud Drive环境" -ForegroundColor Yellow
    Write-Host "启用iCloud兼容模式..." -ForegroundColor Yellow
}

# 高级文件移动函数 - 兼容iCloud Drive
function Advanced-MoveFile {
    param(
        [string]$Source,
        [string]$Destination,
        [string]$Description,
        [switch]$ForceOverwrite = $true
    )
    
    Write-Host "`n--- 处理: $Description ---" -ForegroundColor Cyan
    
    # 检查源文件是否存在
    if (-not (Test-Path $Source)) {
        Write-Host "⚠️  源文件不存在: $Source" -ForegroundColor Yellow
        return $false
    }
    
    # 获取源文件信息
    $sourceItem = Get-Item $Source
    Write-Host "📁 源文件: $Source" -ForegroundColor Gray
    Write-Host "   大小: $($sourceItem.Length) 字节" -ForegroundColor Gray
    Write-Host "   修改时间: $($sourceItem.LastWriteTime)" -ForegroundColor Gray
    
    # 检查iCloud状态
    $icloudStatus = Get-iCloudStatus $Source
    Write-Host "   iCloud状态: $icloudStatus" -ForegroundColor Gray
    
    # 如果是iCloud占位符，先下载
    if ($icloudStatus -eq "iCloud占位符") {
        Write-Host "🔄 正在从iCloud下载文件..." -ForegroundColor Yellow
        try {
            # 尝试访问文件内容以触发下载
            $null = Get-Content $Source -TotalCount 1 -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        }
        catch {
            Write-Host "⚠️  iCloud下载可能失败，继续尝试移动..." -ForegroundColor Yellow
        }
    }
    
    # 确保目标目录存在
    $destinationDir = Split-Path $Destination -Parent
    if (-not (Test-Path $destinationDir)) {
        try {
            New-Item -Path $destinationDir -ItemType Directory -Force | Out-Null
            Write-Host "✅ 创建目标目录: $destinationDir" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ 无法创建目标目录: $destinationDir" -ForegroundColor Red
            Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    
    # 检查目标文件是否已存在
    if (Test-Path $Destination) {
        if ($ForceOverwrite) {
            Write-Host "⚠️  目标文件已存在，将强制覆盖" -ForegroundColor Yellow
            try {
                Remove-Item $Destination -Force
                Write-Host "✅ 已删除现有目标文件" -ForegroundColor Green
            }
            catch {
                Write-Host "❌ 无法删除现有目标文件: $($_.Exception.Message)" -ForegroundColor Red
                return $false
            }
        }
        else {
            Write-Host "❌ 目标文件已存在且未启用强制覆盖" -ForegroundColor Red
            return $false
        }
    }
    
    # 尝试多种移动方法
    $moveSuccess = $false
    
    # 方法1: 使用robocopy (推荐用于iCloud)
    if (-not $moveSuccess -and (Get-Command robocopy -ErrorAction SilentlyContinue)) {
        Write-Host "🔄 尝试使用robocopy移动文件..." -ForegroundColor Yellow
        try {
            $sourceDir = Split-Path $Source -Parent
            $sourceFile = Split-Path $Source -Leaf
            $destinationDir = Split-Path $Destination -Parent
            $destinationFile = Split-Path $Destination -Leaf
            
            # 使用robocopy移动文件
            $robocopyResult = robocopy $sourceDir $destinationDir $sourceFile /MOV /R:3 /W:1 /NP /NDL /NJH /NJS
            
            # 如果文件名需要重命名
            if ($sourceFile -ne $destinationFile) {
                $tempDestination = Join-Path $destinationDir $sourceFile
                if (Test-Path $tempDestination) {
                    Rename-Item $tempDestination $destinationFile -Force
                }
            }
            
            if (Test-Path $Destination -and -not (Test-Path $Source)) {
                $moveSuccess = $true
                Write-Host "✅ robocopy移动成功" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "⚠️  robocopy移动失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # 方法2: 使用Copy-Item + Remove-Item
    if (-not $moveSuccess) {
        Write-Host "🔄 尝试使用Copy-Item + Remove-Item..." -ForegroundColor Yellow
        try {
            Copy-Item $Source $Destination -Force
            if (Test-Path $Destination) {
                Remove-Item $Source -Force
                $moveSuccess = $true
                Write-Host "✅ Copy+Remove移动成功" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "⚠️  Copy+Remove失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
    
    # 方法3: 使用Move-Item (最后尝试)
    if (-not $moveSuccess) {
        Write-Host "🔄 尝试使用Move-Item..." -ForegroundColor Yellow
        try {
            Move-Item $Source $Destination -Force
            if (Test-Path $Destination) {
                $moveSuccess = $true
                Write-Host "✅ Move-Item移动成功" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "❌ Move-Item失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 验证移动结果
    if ($moveSuccess) {
        $destItem = Get-Item $Destination
        Write-Host "📁 目标文件: $Destination" -ForegroundColor Gray
        Write-Host "   大小: $($destItem.Length) 字节" -ForegroundColor Gray
        Write-Host "   修改时间: $($destItem.LastWriteTime)" -ForegroundColor Gray
        
        # 验证文件大小是否一致
        if ($sourceItem.Length -eq $destItem.Length) {
            Write-Host "✅ 文件大小验证通过" -ForegroundColor Green
        }
        else {
            Write-Host "⚠️  文件大小不一致，可能移动不完整" -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "❌ 所有移动方法都失败了" -ForegroundColor Red
    }
    
    return $moveSuccess
}

# 获取iCloud状态函数
function Get-iCloudStatus {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        $item = Get-Item $FilePath
        $attributes = $item.Attributes
        
        if ($attributes -band [System.IO.FileAttributes]::ReparsePoint) {
            return "iCloud占位符"
        }
        elseif ($attributes -band [System.IO.FileAttributes]::Offline) {
            return "iCloud离线"
        }
        else {
            return "本地可用"
        }
    }
    return "文件不存在"
}

# 批量文件移动函数
function Batch-MoveFiles {
    param(
        [hashtable]$FileMapping,
        [string]$Category
    )
    
    Write-Host "`n=== 移动 $Category ===" -ForegroundColor Cyan
    
    $successCount = 0
    $totalCount = $FileMapping.Count
    
    foreach ($sourceFile in $FileMapping.Keys) {
        $destinationFile = $FileMapping[$sourceFile]
        $description = "$Category : $sourceFile"
        
        if (Advanced-MoveFile -Source $sourceFile -Destination $destinationFile -Description $description) {
            $successCount++
        }
        
        # 短暂暂停以避免iCloud同步冲突
        Start-Sleep -Milliseconds 500
    }
    
    Write-Host "`n📊 $Category 移动统计:" -ForegroundColor Yellow
    Write-Host "   成功: $successCount / $totalCount" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })
    
    return $successCount
}

# 主要文件移动操作开始
Write-Host "`n🚀 开始iCloud兼容文件移动操作..." -ForegroundColor Green

$totalMoved = 0

# 1. 移动核心数据文件
$coreDataFiles = @{
    "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" = "02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv"
    "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx"
    "失效模式定义.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx"
}
$totalMoved += Batch-MoveFiles $coreDataFiles "核心数据文件"

# 2. 移动核心分析脚本
$coreScripts = @{
    "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py"
    "g3p_comprehensive_statistical_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_综合统计分析_v1.0.py"
    "g3p_resistance_welding_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_电阻焊接分析_v1.0.py"
}
$totalMoved += Batch-MoveFiles $coreScripts "核心分析脚本"

# 3. 移动参考脚本
$referenceScripts = @{
    "G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py" = "02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py"
    "H1_Analysis_Manual.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py"
    "H1_Data_Verification.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py"
}
$totalMoved += Batch-MoveFiles $referenceScripts "参考脚本"

# 4. 移动可视化文件
$visualizationFiles = @{
    "g3p_data_exploration.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_数据探索_v1.0.png"
    "g3p_h1_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证_v1.0.png"
    "g3p_h2_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证_v1.0.png"
    "g3p_parameter_optimization.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化_v1.0.png"
    "g3p_prediction_models_v2.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_预测模型_v2.0.png"
}
$totalMoved += Batch-MoveFiles $visualizationFiles "可视化文件"

# 5. 移动正式报告
$officialReports = @{
    "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf" = "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf"
    "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pptx" = "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pptx"
    "Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf" = "03_Official_Reports/REPORT_优化参数_G3P_OP60_20250731.pdf"
}
$totalMoved += Batch-MoveFiles $officialReports "正式报告"

# 6. 移动设备文档
$equipmentDocs = @{
    "充气机电阻焊接说明书 - 英文版.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书_EN.pdf"
    "B0742300.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_B0742300.pdf"
    "B0984900.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_B0984900.pdf"
}
$totalMoved += Batch-MoveFiles $equipmentDocs "设备文档"

Write-Host "`n=== iCloud兼容移动操作完成 ===" -ForegroundColor Green
Write-Host "总计成功移动文件数: $totalMoved" -ForegroundColor Green
Write-Host "建议运行诊断脚本验证结果" -ForegroundColor Yellow

# 生成移动操作报告
$moveReport = @"
# iCloud兼容文件移动操作报告

**操作时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
**环境**: $(if ($isICloudEnvironment) { "iCloud Drive" } else { "本地环境" })
**成功移动文件数**: $totalMoved

## 操作特点

### iCloud兼容性改进
- ✅ 自动检测iCloud占位符文件
- ✅ 触发iCloud文件下载
- ✅ 使用robocopy避免同步冲突
- ✅ 多重移动方法备选
- ✅ 强制覆盖处理重复文件

### 错误处理增强
- ✅ 详细的错误信息记录
- ✅ 文件大小验证
- ✅ 移动操作状态确认
- ✅ 批量操作进度跟踪

## 建议后续操作

1. **运行诊断脚本**: `.\DIAGNOSTIC_FILE_AUDIT.ps1`
2. **验证文件完整性**: 检查重要文件是否正确移动
3. **更新文档引用**: 运行文档更新脚本
4. **清理原始位置**: 确认移动成功后清理残留文件

---
**操作完成时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@

Set-Content -Path "ICLOUD_MOVE_OPERATION_REPORT.md" -Value $moveReport -Encoding UTF8
Write-Host "操作报告已保存到: ICLOUD_MOVE_OPERATION_REPORT.md" -ForegroundColor Green
