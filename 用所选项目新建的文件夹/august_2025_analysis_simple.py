#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 8月份2025年测试结果分析 - 简化版
分析8月份的测试数据并审核参数计划
"""

import csv
from collections import defaultdict, Counter

def load_august_data():
    """加载8月份数据"""
    try:
        august_data = []
        
        # 读取CSV文件
        with open('2025-08-03_22-09-59_Raw_Data.csv', 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                # 检查是否为8月份数据
                if '2025/8' in row.get('date_time', ''):
                    august_data.append(row)
        
        print(f"成功加载8月份数据，共{len(august_data)}条记录")
        return august_data
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return []

def analyze_august_results(data):
    """分析8月份测试结果"""
    print("\n" + "="*60)
    print("8月份2025年测试结果分析")
    print("="*60)
    
    if not data:
        print("没有找到8月份数据")
        return
    
    # 基本统计信息
    print(f"\n1. 基本统计信息:")
    print(f"   总测试数量: {len(data)}")
    
    # 获取日期范围
    dates = [row['date_time'] for row in data if row['date_time']]
    if dates:
        print(f"   测试日期范围: {min(dates)} 到 {max(dates)}")
    
    # 按组号分析
    print(f"\n2. 按组号分析:")
    group_counts = Counter(row['group#'] for row in data if row['group#'])
    print("   组号分布:")
    for group, count in group_counts.most_common():
        print(f"   {group}: {count}个样品")
    
    # 焊接质量分析
    print(f"\n3. 焊接质量分析:")
    weld_quality = Counter(row['weld_failure_type'] for row in data if row['weld_failure_type'])
    print("   失效类型分布:")
    for failure_type, count in weld_quality.most_common():
        percentage = (count / len(data)) * 100
        print(f"   {failure_type}: {count}个 ({percentage:.1f}%)")
    
    # 泄漏测试结果
    print(f"\n4. 泄漏测试结果:")
    leakage_results = Counter(row['leakage'] for row in data if row['leakage'])
    print("   泄漏测试结果:")
    for result, count in leakage_results.most_common():
        percentage = (count / len(data)) * 100
        print(f"   {result}: {count}个 ({percentage:.1f}%)")
    
    # 焊接高度分析
    print(f"\n5. 焊接高度分析:")
    heights = []
    for row in data:
        try:
            height = float(row['post_weld_disk_holder_height'])
            heights.append(height)
        except (ValueError, TypeError):
            continue
    
    if heights:
        avg_height = sum(heights) / len(heights)
        min_height = min(heights)
        max_height = max(heights)
        
        # 计算标准差
        variance = sum((h - avg_height) ** 2 for h in heights) / len(heights)
        std_height = variance ** 0.5
        
        print(f"   平均高度: {avg_height:.3f} mm")
        print(f"   高度范围: {min_height:.3f} - {max_height:.3f} mm")
        print(f"   标准差: {std_height:.3f} mm")
    
    return data

def verify_parameter_plan(data):
    """审核参数计划"""
    print("\n" + "="*60)
    print("参数计划审核")
    print("="*60)
    
    # 定义计划参数
    planned_params = {
        'A': {'electrode_pressure': 44, 's1_percent_current': 97, 's1_hold': 6, 's1_cool': 0, 'repeats': 2},
        'B': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 5, 's1_cool': 0, 'repeats': 1},
        'C': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 5, 's1_cool': 1, 'repeats': 1},
        'D': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 7, 's1_cool': 0, 'repeats': 1},
        'E': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 7, 's1_cool': 1, 'repeats': 1},
        'F': {'electrode_pressure': 45, 's1_percent_current': 98, 's1_hold': 5, 's1_cool': 0, 'repeats': 2},
        'G': {'electrode_pressure': 45, 's1_percent_current': 98, 's1_hold': 7, 's1_cool': 0, 'repeats': 2}
    }
    
    print("\n1. 计划参数设置:")
    for group, params in planned_params.items():
        print(f"   组{group}: 电极压力={params['electrode_pressure']}psi, "
              f"S1电流={params['s1_percent_current']}%, "
              f"S1保持时间={params['s1_hold']}c, "
              f"S1冷却={params['s1_cool']}c, "
              f"重复次数={params['repeats']}")
    
    # 分析实际执行情况
    print(f"\n2. 实际执行情况分析:")
    
    # 按组号分析实际参数
    for group in planned_params.keys():
        group_data = [row for row in data if group in row.get('group#', '')]
        if group_data:
            print(f"\n   组{group}实际执行情况:")
            print(f"   实际样品数: {len(group_data)}")
            
            # 检查参数一致性
            if group_data:
                try:
                    actual_pressure = float(group_data[0]['electrode_pressure'])
                    actual_current = float(group_data[0]['s1_percent_current'])
                    actual_hold = float(group_data[0]['s1_hold'])
                    actual_cool = float(group_data[0]['s1_cool'])
                    
                    planned = planned_params[group]
                    
                    print(f"   电极压力: 计划{planned['electrode_pressure']}psi, 实际{actual_pressure}psi "
                          f"({'✓' if actual_pressure == planned['electrode_pressure'] else '✗'})")
                    print(f"   S1电流: 计划{planned['s1_percent_current']}%, 实际{actual_current}% "
                          f"({'✓' if actual_current == planned['s1_percent_current'] else '✗'})")
                    print(f"   S1保持时间: 计划{planned['s1_hold']}c, 实际{actual_hold}c "
                          f"({'✓' if actual_hold == planned['s1_hold'] else '✗'})")
                    print(f"   S1冷却: 计划{planned['s1_cool']}c, 实际{actual_cool}c "
                          f"({'✓' if actual_cool == planned['s1_cool'] else '✗'})")
                    
                    # 质量分析
                    pass_count = sum(1 for row in group_data if row['leakage'] == 'Pass')
                    pass_rate = (pass_count / len(group_data)) * 100
                    print(f"   通过率: {pass_rate:.1f}%")
                except (ValueError, KeyError) as e:
                    print(f"   参数检查出错: {e}")
        else:
            print(f"\n   组{group}: 未找到数据")

def analyze_fixed_parameters(data):
    """分析固定参数设置"""
    print("\n" + "="*60)
    print("固定参数设置分析")
    print("="*60)
    
    # 定义固定参数
    fixed_params = {
        'S1阶段': {
            's1_squeeze': 15,
            's1_weld_heat': 1,
            's1_off': 0,
            's1_impulses': 1,
            's1_valve_mode': 7,
            's1_cycle_mode': 2,
            's1_current_mode': 0,
            's1_slope_count': 0,
            'electrode_height': 210.5
        },
        'S2阶段': {
            's2_squeeze': 5,
            's2_weld_heat': 1,
            's2_off': 0,
            's2_hold': 20,
            's2_cool': 1,
            's2_impulses': 1,
            's2_percent_current': 99,
            's2_current_mode': 0,
            's2_valve_mode': 7,
            's2_cycle_mode': 2,
            's2_slope_count': 0
        }
    }
    
    print("\n1. 固定参数设置:")
    for stage, params in fixed_params.items():
        print(f"\n   {stage}:")
        for param, value in params.items():
            print(f"     {param}: {value}")
    
    # 检查实际执行情况
    print(f"\n2. 固定参数执行情况检查:")
    
    if not data:
        print("   没有数据可检查")
        return
    
    # 检查S1阶段参数
    print(f"\n   S1阶段参数检查:")
    for param, planned_value in fixed_params['S1阶段'].items():
        actual_values = set()
        for row in data:
            if param in row and row[param]:
                try:
                    actual_values.add(float(row[param]))
                except ValueError:
                    continue
        
        if actual_values:
            actual_value = list(actual_values)[0]
            status = "✓" if actual_value == planned_value else "✗"
            print(f"     {param}: 计划{planned_value}, 实际{actual_value} {status}")
    
    # 检查S2阶段参数
    print(f"\n   S2阶段参数检查:")
    for param, planned_value in fixed_params['S2阶段'].items():
        actual_values = set()
        for row in data:
            if param in row and row[param]:
                try:
                    actual_values.add(float(row[param]))
                except ValueError:
                    continue
        
        if actual_values:
            actual_value = list(actual_values)[0]
            status = "✓" if actual_value == planned_value else "✗"
            print(f"     {param}: 计划{planned_value}, 实际{actual_value} {status}")

def generate_summary_report(data):
    """生成总结报告"""
    print("\n" + "="*60)
    print("总结报告")
    print("="*60)
    
    if not data:
        print("没有数据可分析")
        return
    
    # 总体质量统计
    total_samples = len(data)
    pass_samples = sum(1 for row in data if row['leakage'] == 'Pass')
    fail_samples = total_samples - pass_samples
    pass_rate = (pass_samples / total_samples) * 100
    
    print(f"\n1. 总体质量统计:")
    print(f"   总样品数: {total_samples}")
    print(f"   通过样品数: {pass_samples}")
    print(f"   失败样品数: {fail_samples}")
    print(f"   通过率: {pass_rate:.1f}%")
    
    # 主要发现
    print(f"\n2. 主要发现:")
    
    # 分析组号分布
    group_distribution = Counter(row['group#'] for row in data if row['group#'])
    print(f"   ✓ 测试组号分布: {dict(group_distribution)}")
    
    # 分析质量结果
    august_pass_rate = (pass_samples / total_samples) * 100
    print(f"   ✓ 8月份测试通过率: {august_pass_rate:.1f}%")
    
    # 分析失效模式
    failure_modes = Counter(row['weld_failure_type'] for row in data if row['weld_failure_type'])
    if failure_modes:
        print(f"   ✓ 主要失效模式: {dict(failure_modes)}")
    
    # 建议
    print(f"\n3. 建议:")
    print("   - 继续监控8月份测试数据的质量趋势")
    print("   - 对比不同组号的参数设置效果")
    print("   - 分析固定参数设置的稳定性")
    print("   - 根据测试结果优化参数设置")

def main():
    """主函数"""
    print("G3P OP60 8月份2025年测试结果分析")
    print("="*60)
    
    # 加载数据
    data = load_august_data()
    
    # 分析8月份结果
    analyze_august_results(data)
    
    # 审核参数计划
    verify_parameter_plan(data)
    
    # 分析固定参数
    analyze_fixed_parameters(data)
    
    # 生成总结报告
    generate_summary_report(data)
    
    print("\n" + "="*60)
    print("分析完成")
    print("="*60)

if __name__ == "__main__":
    main() 