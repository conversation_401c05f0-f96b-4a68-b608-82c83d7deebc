#!/usr/bin/env python3
"""
G3P OP60电阻焊接工艺统计分析
按照统计分析计划执行6个阶段的完整分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class G3PStatisticalAnalysis:
    def __init__(self, data_file):
        """初始化分析类"""
        self.data_file = data_file
        self.raw_data = None
        self.clean_data = None
        self.double_s1s2_data = None
        self.results = {}
        
    def load_and_inspect_data(self):
        """第一阶段：数据加载与初步检查"""
        print("=" * 60)
        print("第一阶段：数据准备与探索分析")
        print("=" * 60)
        
        # 加载数据
        self.raw_data = pd.read_csv(self.data_file)
        print(f"数据加载完成：{self.raw_data.shape[0]}行 × {self.raw_data.shape[1]}列")
        
        # 基本信息
        print("\n1.1 数据基本信息：")
        print(f"总样本数：{len(self.raw_data)}")
        print(f"字段数：{len(self.raw_data.columns)}")
        
        # 筛选double_s1s2样本
        self.double_s1s2_data = self.raw_data[self.raw_data['schedule_type'] == 'double_s1s2'].copy()
        print(f"double_s1s2样本数：{len(self.double_s1s2_data)}")
        
        # 失效类型分布
        print("\n1.2 失效类型分布：")
        failure_dist = self.double_s1s2_data['weld_failure_type'].value_counts().sort_index()
        for failure_type, count in failure_dist.items():
            percentage = count / len(self.double_s1s2_data) * 100
            print(f"Type {failure_type}: {count}个样本 ({percentage:.1f}%)")
        
        # 缺失值检查
        print("\n1.3 关键字段缺失值检查：")
        key_fields = ['weld_failure_type', 'post_weld_disk_holder_height', 'weld_width_a', 
                     'weld_width_b', 's1_percent_current', 's2_percent_current', 
                     's1_hold', 's2_hold', 'gas_fill_pressure', 'electrode_pressure', 
                     'room_temperature']
        
        missing_info = {}
        for field in key_fields:
            if field in self.double_s1s2_data.columns:
                missing_count = self.double_s1s2_data[field].isnull().sum()
                missing_pct = missing_count / len(self.double_s1s2_data) * 100
                missing_info[field] = {'count': missing_count, 'percentage': missing_pct}
                if missing_count > 0:
                    print(f"{field}: {missing_count}个缺失值 ({missing_pct:.1f}%)")
        
        if not any(info['count'] > 0 for info in missing_info.values()):
            print("关键字段无缺失值")
        
        return self.double_s1s2_data
    
    def clean_and_preprocess_data(self):
        """数据清洗与预处理"""
        print("\n1.4 数据清洗与预处理：")
        
        # 复制数据用于清洗
        self.clean_data = self.double_s1s2_data.copy()
        
        # 处理焊缝宽度缺失值
        weld_width_missing = (self.clean_data['weld_width_a'].isnull() | 
                             self.clean_data['weld_width_b'].isnull())
        
        if weld_width_missing.sum() > 0:
            print(f"焊缝宽度缺失样本：{weld_width_missing.sum()}个")
            # 检查是否与leakage相关
            leakage_failed = self.clean_data['leakage'] == 'Failed'
            
            # 对于leakage=Failed的样本，焊缝宽度设为0
            self.clean_data.loc[weld_width_missing & leakage_failed, 'weld_width_a'] = 0
            self.clean_data.loc[weld_width_missing & leakage_failed, 'weld_width_b'] = 0
            
            # 对于其他缺失值，使用均值插补
            remaining_missing_a = self.clean_data['weld_width_a'].isnull()
            remaining_missing_b = self.clean_data['weld_width_b'].isnull()
            
            if remaining_missing_a.sum() > 0:
                mean_width_a = self.clean_data['weld_width_a'].mean()
                self.clean_data.loc[remaining_missing_a, 'weld_width_a'] = mean_width_a
                print(f"weld_width_a缺失值用均值{mean_width_a:.3f}插补")
            
            if remaining_missing_b.sum() > 0:
                mean_width_b = self.clean_data['weld_width_b'].mean()
                self.clean_data.loc[remaining_missing_b, 'weld_width_b'] = mean_width_b
                print(f"weld_width_b缺失值用均值{mean_width_b:.3f}插补")
        
        # 处理数值型字段，确保为数值类型
        numeric_fields = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold',
                         'electrode_pressure', 'gas_fill_pressure', 'room_temperature',
                         'post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b']

        for field in numeric_fields:
            if field in self.clean_data.columns:
                self.clean_data[field] = pd.to_numeric(self.clean_data[field], errors='coerce')

        # 处理转换后的缺失值
        gas_pressure_missing = self.clean_data['gas_fill_pressure'].isnull().sum()
        electrode_pressure_missing = self.clean_data['electrode_pressure'].isnull().sum()

        if gas_pressure_missing > 0:
            mean_gas_pressure = self.clean_data['gas_fill_pressure'].mean()
            self.clean_data['gas_fill_pressure'].fillna(mean_gas_pressure, inplace=True)
            print(f"gas_fill_pressure缺失值用均值{mean_gas_pressure:.0f}插补")

        if electrode_pressure_missing > 0:
            mean_electrode_pressure = self.clean_data['electrode_pressure'].mean()
            self.clean_data['electrode_pressure'].fillna(mean_electrode_pressure, inplace=True)
            print(f"electrode_pressure缺失值用均值{mean_electrode_pressure:.0f}插补")

        # 创建成功/失效二分类变量
        self.clean_data['is_success'] = (self.clean_data['weld_failure_type'] == 1).astype(int)
        self.clean_data['is_type4_failure'] = (self.clean_data['weld_failure_type'] == 4).astype(int)
        self.clean_data['is_type8_failure'] = (self.clean_data['weld_failure_type'] == 8).astype(int)

        # 创建电流分组（确保数据为数值型）
        s1_current_valid = self.clean_data['s1_percent_current'].notna()
        s2_current_valid = self.clean_data['s2_percent_current'].notna()

        if s1_current_valid.sum() > 0:
            self.clean_data.loc[s1_current_valid, 's1_current_group'] = pd.cut(
                self.clean_data.loc[s1_current_valid, 's1_percent_current'],
                bins=[0, 96, 98, 100],
                labels=['低电流(<96%)', '中电流(96-98%)', '高电流(>98%)'])

        if s2_current_valid.sum() > 0:
            self.clean_data.loc[s2_current_valid, 's2_current_group'] = pd.cut(
                self.clean_data.loc[s2_current_valid, 's2_percent_current'],
                bins=[0, 96, 98, 100],
                labels=['低电流(<96%)', '中电流(96-98%)', '高电流(>98%)'])
        
        print(f"清洗后数据：{len(self.clean_data)}行")
        print("新增变量：is_success, is_type4_failure, is_type8_failure, s1_current_group, s2_current_group")
        
        return self.clean_data
    
    def descriptive_analysis(self):
        """描述性统计分析"""
        print("\n1.5 描述性统计分析：")
        
        # 成功率统计
        success_rate = self.clean_data['is_success'].mean() * 100
        type4_rate = self.clean_data['is_type4_failure'].mean() * 100
        type8_rate = self.clean_data['is_type8_failure'].mean() * 100
        
        print(f"总体成功率：{success_rate:.1f}%")
        print(f"Type 4失效率：{type4_rate:.1f}%")
        print(f"Type 8失效率：{type8_rate:.1f}%")
        
        # 关键参数统计
        print("\n关键工艺参数统计：")
        key_params = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold', 
                     'electrode_pressure', 'gas_fill_pressure', 'room_temperature']
        
        param_stats = {}
        for param in key_params:
            if param in self.clean_data.columns:
                stats_info = {
                    'mean': self.clean_data[param].mean(),
                    'std': self.clean_data[param].std(),
                    'min': self.clean_data[param].min(),
                    'max': self.clean_data[param].max(),
                    'median': self.clean_data[param].median()
                }
                param_stats[param] = stats_info
                print(f"{param}: {stats_info['mean']:.1f}±{stats_info['std']:.1f} "
                      f"[{stats_info['min']:.1f}, {stats_info['max']:.1f}]")
        
        # 质量指标统计
        print("\n质量指标统计：")
        quality_metrics = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b']
        
        for metric in quality_metrics:
            if metric in self.clean_data.columns:
                mean_val = self.clean_data[metric].mean()
                std_val = self.clean_data[metric].std()
                print(f"{metric}: {mean_val:.3f}±{std_val:.3f}mm")
        
        # 保存结果
        self.results['descriptive'] = {
            'success_rate': success_rate,
            'type4_rate': type4_rate,
            'type8_rate': type8_rate,
            'param_stats': param_stats,
            'sample_size': len(self.clean_data)
        }
        
        return self.results['descriptive']
    
    def data_visualization(self):
        """数据可视化探索"""
        print("\n1.6 生成数据可视化图表...")

        # 设置matplotlib后端为非交互式
        import matplotlib
        matplotlib.use('Agg')  # 使用非GUI后端

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('G3P OP60电阻焊接工艺数据探索分析', fontsize=16, fontweight='bold')

        # 1. 失效类型分布
        failure_counts = self.clean_data['weld_failure_type'].value_counts().sort_index()
        colors = ['#2E8B57', '#DC143C', '#FF8C00']  # 绿色(Type1)、红色(Type4)、橙色(Type8)
        bars = axes[0,0].bar(failure_counts.index, failure_counts.values, color=colors)
        axes[0,0].set_title('失效类型分布', fontsize=12, fontweight='bold')
        axes[0,0].set_xlabel('失效类型')
        axes[0,0].set_ylabel('样本数')
        axes[0,0].grid(True, alpha=0.3)
        for i, v in enumerate(failure_counts.values):
            axes[0,0].text(failure_counts.index[i], v + 1, str(v), ha='center', fontweight='bold')

        # 2. S1电流分布
        axes[0,1].hist(self.clean_data['s1_percent_current'], bins=20, alpha=0.7, color='#4169E1', edgecolor='black')
        axes[0,1].set_title('S1电流分布', fontsize=12, fontweight='bold')
        axes[0,1].set_xlabel('S1电流百分比(%)')
        axes[0,1].set_ylabel('频数')
        axes[0,1].grid(True, alpha=0.3)

        # 3. S2电流分布
        axes[0,2].hist(self.clean_data['s2_percent_current'], bins=20, alpha=0.7, color='#32CD32', edgecolor='black')
        axes[0,2].set_title('S2电流分布', fontsize=12, fontweight='bold')
        axes[0,2].set_xlabel('S2电流百分比(%)')
        axes[0,2].set_ylabel('频数')
        axes[0,2].grid(True, alpha=0.3)

        # 4. 焊接高度分布
        axes[1,0].hist(self.clean_data['post_weld_disk_holder_height'], bins=20, alpha=0.7, color='#FF6347', edgecolor='black')
        axes[1,0].axvline(2.1, color='red', linestyle='--', linewidth=2, label='下限2.1mm')
        axes[1,0].axvline(2.4, color='red', linestyle='--', linewidth=2, label='上限2.4mm')
        axes[1,0].set_title('焊接高度分布', fontsize=12, fontweight='bold')
        axes[1,0].set_xlabel('焊接高度(mm)')
        axes[1,0].set_ylabel('频数')
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].legend()

        # 5. 电流vs成功率
        current_success = self.clean_data.groupby('s2_percent_current')['is_success'].agg(['mean', 'count'])
        valid_currents = current_success[current_success['count'] >= 3]  # 至少3个样本
        scatter = axes[1,1].scatter(valid_currents.index, valid_currents['mean'] * 100,
                         s=valid_currents['count']*10, alpha=0.7, color='#9370DB', edgecolors='black')
        axes[1,1].set_title('S2电流vs成功率', fontsize=12, fontweight='bold')
        axes[1,1].set_xlabel('S2电流百分比(%)')
        axes[1,1].set_ylabel('成功率(%)')
        axes[1,1].grid(True, alpha=0.3)
        axes[1,1].set_ylim(0, 105)

        # 6. 温度vs成功率
        temp_bins = pd.cut(self.clean_data['room_temperature'], bins=5)
        temp_success = self.clean_data.groupby(temp_bins)['is_success'].mean() * 100
        temp_success.plot(kind='bar', ax=axes[1,2], color='#FF8C00', alpha=0.7, edgecolor='black')
        axes[1,2].set_title('环境温度vs成功率', fontsize=12, fontweight='bold')
        axes[1,2].set_xlabel('环境温度区间(°C)')
        axes[1,2].set_ylabel('成功率(%)')
        axes[1,2].tick_params(axis='x', rotation=45)
        axes[1,2].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('g3p_data_exploration.png', dpi=300, bbox_inches='tight')
        plt.close()  # 关闭图形以释放内存

        print("数据探索图表已生成：g3p_data_exploration.png")
        
    def stage1_summary(self):
        """第一阶段总结"""
        print("\n" + "="*60)
        print("第一阶段完成总结")
        print("="*60)
        
        results = self.results['descriptive']
        
        print("1. 数据质量评估：")
        print(f"   - 有效样本：{results['sample_size']}个double_s1s2样本")
        print(f"   - 数据完整性：关键字段无重大缺失")
        print(f"   - 数据质量：良好，可进行统计分析")
        
        print("\n2. 当前工艺状态：")
        print(f"   - 总体成功率：{results['success_rate']:.1f}%")
        print(f"   - Type 4高度失效率：{results['type4_rate']:.1f}%")
        print(f"   - Type 8裂纹失效率：{results['type8_rate']:.1f}%")
        
        print("\n3. 关键发现：")
        if results['success_rate'] < 70:
            print(f"   - 成功率{results['success_rate']:.1f}%偏低，有较大改进空间")
        if results['type4_rate'] > 10:
            print(f"   - Type 4失效率{results['type4_rate']:.1f}%较高，需重点关注高度控制")
        if results['type8_rate'] > 2:
            print(f"   - Type 8失效率{results['type8_rate']:.1f}%需要关注裂纹控制")
        
        print("\n4. 数据准备状态：")
        print("   ✓ 数据清洗完成")
        print("   ✓ 变量编码完成") 
        print("   ✓ 分组变量创建完成")
        print("   ✓ 可视化分析完成")
        print("   ✓ 准备进入H1假设验证阶段")
        
        return True

    def h1_descriptive_analysis(self):
        """H1假设：Type 8失效描述性分析"""
        print("\n" + "="*60)
        print("第二阶段：H1假设验证 - 99%电流与Type 8裂纹失效相关性")
        print("="*60)

        print("2.1 Type 8失效描述性分析：")

        # Type 8失效样本
        type8_samples = self.clean_data[self.clean_data['is_type8_failure'] == 1]
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]

        print(f"Type 8失效样本：{len(type8_samples)}个")
        print(f"成功样本：{len(success_samples)}个")

        # S1电流比较
        print("\nS1电流比较：")
        s1_type8_mean = type8_samples['s1_percent_current'].mean()
        s1_type8_std = type8_samples['s1_percent_current'].std()
        s1_success_mean = success_samples['s1_percent_current'].mean()
        s1_success_std = success_samples['s1_percent_current'].std()

        print(f"Type 8失效 S1电流：{s1_type8_mean:.1f}±{s1_type8_std:.1f}%")
        print(f"成功样本 S1电流：{s1_success_mean:.1f}±{s1_success_std:.1f}%")
        print(f"差异：{s1_type8_mean - s1_success_mean:+.1f}%")

        # S2电流比较
        print("\nS2电流比较：")
        s2_type8_mean = type8_samples['s2_percent_current'].mean()
        s2_type8_std = type8_samples['s2_percent_current'].std()
        s2_success_mean = success_samples['s2_percent_current'].mean()
        s2_success_std = success_samples['s2_percent_current'].std()

        print(f"Type 8失效 S2电流：{s2_type8_mean:.1f}±{s2_type8_std:.1f}%")
        print(f"成功样本 S2电流：{s2_success_mean:.1f}±{s2_success_std:.1f}%")
        print(f"差异：{s2_type8_mean - s2_success_mean:+.1f}%")

        # 99%电流分析
        print("\n99%电流分析：")
        s1_99_samples = self.clean_data[self.clean_data['s1_percent_current'] == 99]
        s2_99_samples = self.clean_data[self.clean_data['s2_percent_current'] == 99]

        s1_99_type8_rate = s1_99_samples['is_type8_failure'].mean() * 100
        s2_99_type8_rate = s2_99_samples['is_type8_failure'].mean() * 100
        overall_type8_rate = self.clean_data['is_type8_failure'].mean() * 100

        print(f"S1=99%样本的Type 8失效率：{s1_99_type8_rate:.1f}% (n={len(s1_99_samples)})")
        print(f"S2=99%样本的Type 8失效率：{s2_99_type8_rate:.1f}% (n={len(s2_99_samples)})")
        print(f"总体Type 8失效率：{overall_type8_rate:.1f}%")

        # 保存结果
        self.results['h1_descriptive'] = {
            's1_type8_mean': s1_type8_mean,
            's1_success_mean': s1_success_mean,
            's2_type8_mean': s2_type8_mean,
            's2_success_mean': s2_success_mean,
            's1_99_type8_rate': s1_99_type8_rate,
            's2_99_type8_rate': s2_99_type8_rate,
            'overall_type8_rate': overall_type8_rate
        }

        return self.results['h1_descriptive']

    def h1_correlation_analysis(self):
        """H1假设：相关性分析"""
        print("\n2.2 电流-裂纹相关性分析：")

        # Pearson相关分析
        s1_corr = stats.pearsonr(self.clean_data['s1_percent_current'],
                                self.clean_data['is_type8_failure'])
        s2_corr = stats.pearsonr(self.clean_data['s2_percent_current'],
                                self.clean_data['is_type8_failure'])

        print(f"S1电流与Type 8失效Pearson相关：r={s1_corr[0]:.3f}, p={s1_corr[1]:.3f}")
        print(f"S2电流与Type 8失效Pearson相关：r={s2_corr[0]:.3f}, p={s2_corr[1]:.3f}")

        # Spearman秩相关分析
        s1_spearman = stats.spearmanr(self.clean_data['s1_percent_current'],
                                     self.clean_data['is_type8_failure'])
        s2_spearman = stats.spearmanr(self.clean_data['s2_percent_current'],
                                     self.clean_data['is_type8_failure'])

        print(f"S1电流与Type 8失效Spearman相关：ρ={s1_spearman[0]:.3f}, p={s1_spearman[1]:.3f}")
        print(f"S2电流与Type 8失效Spearman相关：ρ={s2_spearman[0]:.3f}, p={s2_spearman[1]:.3f}")

        # 相关性强度评估
        def interpret_correlation(r):
            abs_r = abs(r)
            if abs_r < 0.1:
                return "极弱"
            elif abs_r < 0.3:
                return "弱"
            elif abs_r < 0.5:
                return "中等"
            elif abs_r < 0.7:
                return "强"
            else:
                return "极强"

        print(f"\n相关性强度评估：")
        print(f"S1电流相关性：{interpret_correlation(s1_corr[0])}")
        print(f"S2电流相关性：{interpret_correlation(s2_corr[0])}")

        # 保存结果
        self.results['h1_correlation'] = {
            's1_pearson_r': s1_corr[0],
            's1_pearson_p': s1_corr[1],
            's2_pearson_r': s2_corr[0],
            's2_pearson_p': s2_corr[1],
            's1_spearman_r': s1_spearman[0],
            's1_spearman_p': s1_spearman[1],
            's2_spearman_r': s2_spearman[0],
            's2_spearman_p': s2_spearman[1]
        }

        return self.results['h1_correlation']

    def h1_chi_square_test(self):
        """H1假设：卡方检验"""
        print("\n2.3 99%电流与裂纹失效卡方检验：")

        # S1电流卡方检验
        s1_contingency = pd.crosstab(self.clean_data['s1_percent_current'] == 99,
                                    self.clean_data['is_type8_failure'])

        if s1_contingency.shape == (2, 2):
            chi2_s1, p_s1, dof_s1, expected_s1 = stats.chi2_contingency(s1_contingency)
            cramers_v_s1 = np.sqrt(chi2_s1 / (len(self.clean_data) * (min(s1_contingency.shape) - 1)))

            print("S1电流=99% vs Type 8失效列联表：")
            print(s1_contingency)
            print(f"卡方统计量：χ²={chi2_s1:.3f}, p={p_s1:.3f}")
            print(f"Cramer's V：{cramers_v_s1:.3f}")
        else:
            print("S1电流数据不足以进行卡方检验")
            chi2_s1, p_s1, cramers_v_s1 = np.nan, np.nan, np.nan

        # S2电流卡方检验
        s2_contingency = pd.crosstab(self.clean_data['s2_percent_current'] == 99,
                                    self.clean_data['is_type8_failure'])

        if s2_contingency.shape == (2, 2):
            chi2_s2, p_s2, dof_s2, expected_s2 = stats.chi2_contingency(s2_contingency)
            cramers_v_s2 = np.sqrt(chi2_s2 / (len(self.clean_data) * (min(s2_contingency.shape) - 1)))

            print("\nS2电流=99% vs Type 8失效列联表：")
            print(s2_contingency)
            print(f"卡方统计量：χ²={chi2_s2:.3f}, p={p_s2:.3f}")
            print(f"Cramer's V：{cramers_v_s2:.3f}")
        else:
            print("S2电流数据不足以进行卡方检验")
            chi2_s2, p_s2, cramers_v_s2 = np.nan, np.nan, np.nan

        # 保存结果
        self.results['h1_chi_square'] = {
            's1_chi2': chi2_s1,
            's1_p_value': p_s1,
            's1_cramers_v': cramers_v_s1,
            's2_chi2': chi2_s2,
            's2_p_value': p_s2,
            's2_cramers_v': cramers_v_s2
        }

        return self.results['h1_chi_square']

    def h1_logistic_regression(self):
        """H1假设：逻辑回归分析"""
        print("\n2.4 Type 8失效逻辑回归分析：")

        # 准备数据
        features = ['s1_percent_current', 's2_percent_current', 'weld_width_a', 'weld_width_b']
        X = self.clean_data[features].copy()
        y = self.clean_data['is_type8_failure']

        # 处理缺失值
        X = X.fillna(X.mean())

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 训练逻辑回归模型
        lr_model = LogisticRegression(random_state=42)
        lr_model.fit(X_scaled, y)

        # 预测和评估
        y_pred = lr_model.predict(X_scaled)
        y_pred_proba = lr_model.predict_proba(X_scaled)[:, 1]

        # 计算AUC
        if len(np.unique(y)) > 1:
            auc_score = roc_auc_score(y, y_pred_proba)
        else:
            auc_score = np.nan

        # 回归系数和OR值
        coefficients = lr_model.coef_[0]
        or_values = np.exp(coefficients)

        print("逻辑回归结果：")
        for i, feature in enumerate(features):
            print(f"{feature}: 系数={coefficients[i]:.3f}, OR={or_values[i]:.3f}")

        print(f"\n模型性能：")
        print(f"AUC: {auc_score:.3f}")

        # 分类报告
        if len(np.unique(y)) > 1:
            print("\n分类报告：")
            print(classification_report(y, y_pred))

        # 保存结果
        self.results['h1_logistic'] = {
            'coefficients': dict(zip(features, coefficients)),
            'or_values': dict(zip(features, or_values)),
            'auc_score': auc_score,
            'model': lr_model,
            'scaler': scaler
        }

        return self.results['h1_logistic']

    def h1_hypothesis_conclusion(self):
        """H1假设验证结论"""
        print("\n" + "="*60)
        print("H1假设验证结论")
        print("="*60)

        # 获取分析结果
        desc_results = self.results['h1_descriptive']
        corr_results = self.results['h1_correlation']
        chi2_results = self.results['h1_chi_square']
        lr_results = self.results['h1_logistic']

        print("H1假设：99%电流与Type 8裂纹失效存在显著相关性")
        print("\n验证结果：")

        # 1. 描述性证据
        print("1. 描述性证据：")
        s1_diff = desc_results['s1_type8_mean'] - desc_results['s1_success_mean']
        s2_diff = desc_results['s2_type8_mean'] - desc_results['s2_success_mean']
        print(f"   - Type 8失效样本S1电流比成功样本高{s1_diff:+.1f}%")
        print(f"   - Type 8失效样本S2电流比成功样本高{s2_diff:+.1f}%")
        print(f"   - S2=99%样本Type 8失效率：{desc_results['s2_99_type8_rate']:.1f}%")

        # 2. 统计显著性
        print("\n2. 统计显著性：")
        print(f"   - S1电流相关性：r={corr_results['s1_pearson_r']:.3f}, p={corr_results['s1_pearson_p']:.3f}")
        print(f"   - S2电流相关性：r={corr_results['s2_pearson_r']:.3f}, p={corr_results['s2_pearson_p']:.3f}")

        if not np.isnan(chi2_results['s2_p_value']):
            print(f"   - S2卡方检验：p={chi2_results['s2_p_value']:.3f}")

        print(f"   - 逻辑回归AUC：{lr_results['auc_score']:.3f}")

        # 3. 效应大小
        print("\n3. 效应大小：")
        if not np.isnan(chi2_results['s2_cramers_v']):
            print(f"   - S2 Cramer's V：{chi2_results['s2_cramers_v']:.3f}")

        s2_or = lr_results['or_values']['s2_percent_current']
        print(f"   - S2电流OR值：{s2_or:.3f}")

        # 4. 假设验证结论
        print("\n4. H1假设验证结论：")

        # 判断标准
        significant_correlation = (abs(corr_results['s2_pearson_r']) > 0.3 and
                                 corr_results['s2_pearson_p'] < 0.05)
        significant_chi2 = (not np.isnan(chi2_results['s2_p_value']) and
                           chi2_results['s2_p_value'] < 0.05)
        good_auc = lr_results['auc_score'] > 0.7

        if significant_correlation or significant_chi2 or good_auc:
            conclusion = "H1假设得到支持"
            print(f"   ✓ {conclusion}")
            print("   - 高电流（特别是S2=99%）与Type 8裂纹失效存在统计显著关联")
        else:
            conclusion = "H1假设证据不足"
            print(f"   ✗ {conclusion}")
            print("   - 电流与Type 8失效的关联性未达到统计显著水平")

        # 5. 工程意义解释
        print("\n5. 工程意义解释：")
        if s2_diff > 0:
            print(f"   - Type 8失效样本平均S2电流高{s2_diff:.1f}%，提示高电流增加裂纹风险")
        if desc_results['s2_99_type8_rate'] > desc_results['overall_type8_rate']:
            risk_increase = desc_results['s2_99_type8_rate'] - desc_results['overall_type8_rate']
            print(f"   - S2=99%时裂纹风险增加{risk_increase:.1f}个百分点")

        # 6. 工艺优化建议
        print("\n6. 基于H1验证的工艺优化建议：")
        if s2_diff > 1:
            print("   - 建议控制S2电流≤98%以降低裂纹风险")
        if desc_results['s2_99_type8_rate'] > 10:
            print("   - 避免使用S2=99%参数组合")
        print("   - 优化电流曲线，避免过高峰值电流")
        print("   - 考虑延长S2保持时间以补偿电流降低")

        # 保存结论
        self.results['h1_conclusion'] = {
            'hypothesis_supported': significant_correlation or significant_chi2 or good_auc,
            'conclusion': conclusion,
            'key_findings': {
                's1_current_diff': s1_diff,
                's2_current_diff': s2_diff,
                'significant_correlation': significant_correlation,
                'significant_chi2': significant_chi2,
                'good_prediction': good_auc
            }
        }

        return self.results['h1_conclusion']

    def h2_multifactor_anova(self):
        """H2假设：Type 4失效多因素方差分析"""
        print("\n" + "="*60)
        print("第三阶段：H2假设验证 - Type 4高度失效多因素根因分析")
        print("="*60)

        print("3.1 Type 4失效多因素方差分析：")

        # Type 4失效样本分析
        type4_samples = self.clean_data[self.clean_data['is_type4_failure'] == 1]
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]

        print(f"Type 4失效样本：{len(type4_samples)}个")
        print(f"成功样本：{len(success_samples)}个")

        # 关键因子比较分析
        key_factors = ['gas_fill_pressure', 's2_hold', 'electrode_pressure', 'room_temperature']

        print("\n关键因子比较：")
        factor_comparison = {}

        for factor in key_factors:
            if factor in self.clean_data.columns:
                type4_mean = type4_samples[factor].mean()
                type4_std = type4_samples[factor].std()
                success_mean = success_samples[factor].mean()
                success_std = success_samples[factor].std()

                # t检验
                t_stat, p_value = stats.ttest_ind(type4_samples[factor].dropna(),
                                                 success_samples[factor].dropna())

                # Cohen's d效应大小
                pooled_std = np.sqrt(((len(type4_samples)-1)*type4_std**2 +
                                    (len(success_samples)-1)*success_std**2) /
                                   (len(type4_samples) + len(success_samples) - 2))
                cohens_d = (type4_mean - success_mean) / pooled_std

                factor_comparison[factor] = {
                    'type4_mean': type4_mean,
                    'type4_std': type4_std,
                    'success_mean': success_mean,
                    'success_std': success_std,
                    't_stat': t_stat,
                    'p_value': p_value,
                    'cohens_d': cohens_d
                }

                print(f"{factor}:")
                print(f"  Type 4失效：{type4_mean:.1f}±{type4_std:.1f}")
                print(f"  成功样本：{success_mean:.1f}±{success_std:.1f}")
                print(f"  差异：{type4_mean - success_mean:+.1f}")
                print(f"  t检验：t={t_stat:.3f}, p={p_value:.3f}")
                print(f"  Cohen's d：{cohens_d:.3f}")
                print()

        # 焊接高度分析
        print("焊接高度分析：")
        height_type4 = type4_samples['post_weld_disk_holder_height']
        height_success = success_samples['post_weld_disk_holder_height']

        height_t_stat, height_p = stats.ttest_ind(height_type4.dropna(), height_success.dropna())

        print(f"Type 4失效高度：{height_type4.mean():.3f}±{height_type4.std():.3f}mm")
        print(f"成功样本高度：{height_success.mean():.3f}±{height_success.std():.3f}mm")
        print(f"高度差异：{height_type4.mean() - height_success.mean():+.3f}mm")
        print(f"t检验：t={height_t_stat:.3f}, p={height_p:.3f}")

        # 保存结果
        self.results['h2_anova'] = {
            'factor_comparison': factor_comparison,
            'height_analysis': {
                'type4_mean': height_type4.mean(),
                'success_mean': height_success.mean(),
                't_stat': height_t_stat,
                'p_value': height_p
            }
        }

        return self.results['h2_anova']

    def h2_multiple_regression(self):
        """H2假设：多元线性回归分析"""
        print("\n3.2 焊接高度多元回归分析：")

        # 准备回归数据
        regression_features = ['gas_fill_pressure', 's2_hold', 'electrode_pressure',
                              'room_temperature', 's1_percent_current', 's2_percent_current']

        X = self.clean_data[regression_features].copy()
        y = self.clean_data['post_weld_disk_holder_height']

        # 处理缺失值
        X = X.fillna(X.mean())
        y = y.fillna(y.mean())

        # 标准化特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        # 多元线性回归
        lr_model = LinearRegression()
        lr_model.fit(X_scaled, y)

        # 预测和评估
        y_pred = lr_model.predict(X_scaled)
        r2_score = lr_model.score(X_scaled, y)

        # 计算调整R²
        n = len(y)
        p = len(regression_features)
        adjusted_r2 = 1 - (1 - r2_score) * (n - 1) / (n - p - 1)

        # 残差分析
        residuals = y - y_pred
        rmse = np.sqrt(np.mean(residuals**2))

        print("多元回归结果：")
        print(f"R²：{r2_score:.3f}")
        print(f"调整R²：{adjusted_r2:.3f}")
        print(f"RMSE：{rmse:.3f}mm")

        # 回归系数分析
        print("\n回归系数：")
        coefficients = lr_model.coef_
        for i, feature in enumerate(regression_features):
            print(f"{feature}: {coefficients[i]:.6f}")

        # 特征重要性（基于标准化系数的绝对值）
        feature_importance = pd.DataFrame({
            'feature': regression_features,
            'coefficient': coefficients,
            'abs_coefficient': np.abs(coefficients)
        }).sort_values('abs_coefficient', ascending=False)

        print("\n特征重要性排序：")
        for _, row in feature_importance.iterrows():
            print(f"{row['feature']}: {row['coefficient']:.6f} (|{row['abs_coefficient']:.6f}|)")

        # 保存结果
        self.results['h2_regression'] = {
            'r2_score': r2_score,
            'adjusted_r2': adjusted_r2,
            'rmse': rmse,
            'coefficients': dict(zip(regression_features, coefficients)),
            'feature_importance': feature_importance,
            'model': lr_model,
            'scaler': scaler
        }

        return self.results['h2_regression']

    def h2_decision_tree_analysis(self):
        """H2假设：决策树分析"""
        print("\n3.3 Type 4失效决策树分析：")

        # 准备数据
        tree_features = ['gas_fill_pressure', 's2_hold', 'electrode_pressure',
                        'room_temperature', 'post_weld_disk_holder_height']

        X = self.clean_data[tree_features].copy()
        y = self.clean_data['is_type4_failure']

        # 处理缺失值
        X = X.fillna(X.mean())

        # 训练决策树
        dt_model = DecisionTreeClassifier(max_depth=4, min_samples_split=10,
                                         min_samples_leaf=5, random_state=42)
        dt_model.fit(X, y)

        # 预测和评估
        y_pred = dt_model.predict(X)
        accuracy = dt_model.score(X, y)

        # 特征重要性
        feature_importance = pd.DataFrame({
            'feature': tree_features,
            'importance': dt_model.feature_importances_
        }).sort_values('importance', ascending=False)

        print("决策树分析结果：")
        print(f"准确率：{accuracy:.3f}")

        print("\n特征重要性：")
        for _, row in feature_importance.iterrows():
            print(f"{row['feature']}: {row['importance']:.3f}")

        # 决策规则提取（简化版）
        print("\n关键决策规则：")

        # 分析高度阈值
        type4_heights = self.clean_data[self.clean_data['is_type4_failure'] == 1]['post_weld_disk_holder_height']
        success_heights = self.clean_data[self.clean_data['is_success'] == 1]['post_weld_disk_holder_height']

        # 找到最佳分割点
        height_threshold = (type4_heights.mean() + success_heights.mean()) / 2

        print(f"1. 焊接高度 > {height_threshold:.3f}mm 时，Type 4失效风险增加")

        # 分析其他关键阈值
        for feature in ['gas_fill_pressure', 's2_hold', 'electrode_pressure']:
            type4_values = self.clean_data[self.clean_data['is_type4_failure'] == 1][feature]
            success_values = self.clean_data[self.clean_data['is_success'] == 1][feature]

            if len(type4_values) > 0 and len(success_values) > 0:
                threshold = (type4_values.mean() + success_values.mean()) / 2
                if type4_values.mean() > success_values.mean():
                    direction = ">"
                else:
                    direction = "<"
                print(f"2. {feature} {direction} {threshold:.1f} 时，Type 4失效风险增加")

        # 保存结果
        self.results['h2_decision_tree'] = {
            'accuracy': accuracy,
            'feature_importance': feature_importance,
            'height_threshold': height_threshold,
            'model': dt_model
        }

        return self.results['h2_decision_tree']

    def h2_hypothesis_conclusion(self):
        """H2假设验证结论"""
        print("\n" + "="*60)
        print("H2假设验证结论")
        print("="*60)

        # 获取分析结果
        anova_results = self.results['h2_anova']
        regression_results = self.results['h2_regression']
        tree_results = self.results['h2_decision_tree']

        print("H2假设：Type 4高度失效由多因素驱动（充气压力、S2保持时间、电极压力）")
        print("\n验证结果：")

        # 1. 多因素显著性分析
        print("1. 多因素显著性分析：")
        significant_factors = []
        for factor, stats in anova_results['factor_comparison'].items():
            if stats['p_value'] < 0.05:
                significant_factors.append(factor)
                print(f"   ✓ {factor}: p={stats['p_value']:.3f}, Cohen's d={stats['cohens_d']:.3f}")
            else:
                print(f"   - {factor}: p={stats['p_value']:.3f}, Cohen's d={stats['cohens_d']:.3f}")

        # 2. 回归模型拟合度
        print("\n2. 多元回归模型评估：")
        print(f"   - R²：{regression_results['r2_score']:.3f}")
        print(f"   - 调整R²：{regression_results['adjusted_r2']:.3f}")
        print(f"   - RMSE：{regression_results['rmse']:.3f}mm")

        # 判断模型质量
        good_fit = regression_results['r2_score'] > 0.6
        acceptable_fit = regression_results['adjusted_r2'] > 0.5

        if good_fit:
            print("   ✓ 模型拟合度良好")
        elif acceptable_fit:
            print("   ○ 模型拟合度可接受")
        else:
            print("   ✗ 模型拟合度不足")

        # 3. 关键影响因子
        print("\n3. 关键影响因子识别：")
        top_features = regression_results['feature_importance'].head(3)
        for _, row in top_features.iterrows():
            print(f"   - {row['feature']}: 系数={row['coefficient']:.6f}")

        # 4. 决策树验证
        print("\n4. 决策树分析验证：")
        print(f"   - 分类准确率：{tree_results['accuracy']:.3f}")

        tree_top_features = tree_results['feature_importance'].head(3)
        print("   - 关键特征重要性：")
        for _, row in tree_top_features.iterrows():
            print(f"     {row['feature']}: {row['importance']:.3f}")

        # 5. H2假设验证结论
        print("\n5. H2假设验证结论：")

        # 判断标准
        has_significant_factors = len(significant_factors) >= 2
        has_good_model = good_fit or acceptable_fit
        has_good_prediction = tree_results['accuracy'] > 0.75

        if has_significant_factors and has_good_model:
            conclusion = "H2假设得到强支持"
            print(f"   ✓ {conclusion}")
            print("   - 多个因素对Type 4失效具有统计显著影响")
            print("   - 多元回归模型具有良好的预测能力")
        elif has_significant_factors or has_good_model:
            conclusion = "H2假设得到部分支持"
            print(f"   ○ {conclusion}")
            print("   - 部分因素显示统计显著性或模型具有一定预测能力")
        else:
            conclusion = "H2假设证据不足"
            print(f"   ✗ {conclusion}")
            print("   - 多因素影响未达到统计显著水平")

        # 6. 关键发现
        print("\n6. 关键发现：")

        # 高度差异分析
        height_diff = (anova_results['height_analysis']['type4_mean'] -
                      anova_results['height_analysis']['success_mean'])
        print(f"   - Type 4失效样本平均高度比成功样本高{height_diff:+.3f}mm")

        # 最重要的影响因子
        most_important = regression_results['feature_importance'].iloc[0]
        print(f"   - 最重要影响因子：{most_important['feature']} (系数={most_important['coefficient']:.6f})")

        # 显著因子总结
        if significant_factors:
            print(f"   - 统计显著因子：{', '.join(significant_factors)}")

        # 7. 工艺优化建议
        print("\n7. 基于H2验证的工艺优化建议：")

        # 基于显著因子的建议
        for factor in significant_factors:
            stats = anova_results['factor_comparison'][factor]
            if stats['type4_mean'] > stats['success_mean']:
                print(f"   - 降低{factor}以减少Type 4失效风险")
            else:
                print(f"   - 提高{factor}以减少Type 4失效风险")

        # 基于回归系数的建议
        gas_pressure_coef = regression_results['coefficients'].get('gas_fill_pressure', 0)
        s2_hold_coef = regression_results['coefficients'].get('s2_hold', 0)
        electrode_pressure_coef = regression_results['coefficients'].get('electrode_pressure', 0)

        if abs(gas_pressure_coef) > 0.01:
            if gas_pressure_coef > 0:
                print("   - 适当降低充气压力以控制焊接高度")
            else:
                print("   - 适当提高充气压力以改善焊接质量")

        if abs(s2_hold_coef) > 0.01:
            if s2_hold_coef < 0:
                print("   - 延长S2保持时间以改善焊接高度控制")
            else:
                print("   - 优化S2保持时间以平衡质量和效率")

        if abs(electrode_pressure_coef) > 0.01:
            if electrode_pressure_coef < 0:
                print("   - 提高电极压力以改善焊接高度控制")
            else:
                print("   - 优化电极压力以避免过度压缩")

        # 综合建议
        print("   - 建立多参数协同控制策略")
        print("   - 重点监控焊接高度在线测量")
        print("   - 优化参数窗口以平衡多个质量指标")

        # 保存结论
        self.results['h2_conclusion'] = {
            'hypothesis_supported': has_significant_factors and has_good_model,
            'conclusion': conclusion,
            'significant_factors': significant_factors,
            'model_quality': {
                'r2_score': regression_results['r2_score'],
                'adjusted_r2': regression_results['adjusted_r2'],
                'tree_accuracy': tree_results['accuracy']
            },
            'key_findings': {
                'height_difference': height_diff,
                'most_important_factor': most_important['feature'],
                'significant_factor_count': len(significant_factors)
            }
        }

        return self.results['h2_conclusion']

    def h2_hypothesis_conclusion_v2(self):
        """H2假设验证结论 - 重构版本：分离单因素与多因素效应"""
        print("\n" + "="*60)
        print("H2假设验证结论 (重构版本)")
        print("="*60)

        # 获取分析结果
        anova_results = self.results['h2_anova']
        regression_results = self.results['h2_regression']
        tree_results = self.results['h2_decision_tree']

        print("H2假设：Type 4高度失效由多因素驱动（充气压力、S2保持时间、电极压力）")
        print("\n重构验证框架：分离单因素效应与多因素协同效应")

        # 1. 单因素效应分析
        print("\n1. 单因素效应分析：")
        significant_single_factors = []
        marginal_single_factors = []

        for factor, stats in anova_results['factor_comparison'].items():
            p_value = stats['p_value']
            effect_size = stats.get('cohens_d', 0.0)

            if p_value < 0.05:
                significant_single_factors.append((factor, p_value, effect_size))
                print(f"   ✓ {factor}: p={p_value:.3f}, Cohen's d={effect_size:.3f} (显著)")
            elif p_value < 0.10:
                marginal_single_factors.append((factor, p_value, effect_size))
                print(f"   ○ {factor}: p={p_value:.3f}, Cohen's d={effect_size:.3f} (边缘显著)")
            else:
                print(f"   - {factor}: p={p_value:.3f}, Cohen's d={effect_size:.3f} (不显著)")

        print(f"\n   显著单因素：{len(significant_single_factors)}个")
        print(f"   边缘显著单因素：{len(marginal_single_factors)}个")

        # 2. 多因素协同效应分析
        print("\n2. 多因素协同效应分析：")
        r2_score = regression_results['r2_score']
        adjusted_r2 = regression_results['adjusted_r2']
        rmse = regression_results['rmse']

        print(f"   - 多元回归R²：{r2_score:.3f}")
        print(f"   - 调整R²：{adjusted_r2:.3f}")
        print(f"   - RMSE：{rmse:.3f}mm")

        # 协同效应质量评估（更严格标准）
        strong_synergy = r2_score >= 0.6  # 强协同效应
        moderate_synergy = r2_score >= 0.4  # 中等协同效应
        weak_synergy = r2_score >= 0.2  # 弱协同效应

        if strong_synergy:
            synergy_level = "强"
            print("   ✓ 多因素协同效应强 (R² ≥ 0.6)")
        elif moderate_synergy:
            synergy_level = "中等"
            print("   ○ 多因素协同效应中等 (0.4 ≤ R² < 0.6)")
        elif weak_synergy:
            synergy_level = "弱"
            print("   △ 多因素协同效应弱 (0.2 ≤ R² < 0.4)")
        else:
            synergy_level = "无"
            print("   ✗ 多因素协同效应不明显 (R² < 0.2)")

        # 3. 综合H2假设验证结论（新逻辑）
        print("\n3. H2假设综合验证结论：")

        # 新的判断逻辑：分层评估
        has_significant_single = len(significant_single_factors) >= 1
        has_strong_synergy = strong_synergy
        has_moderate_synergy = moderate_synergy

        if has_significant_single and has_strong_synergy:
            conclusion = "H2假设强支持"
            support_level = "strong"
            print(f"   ✓ {conclusion}")
            print("   - 存在显著单因素效应且多因素协同效应强")
            print(f"   - 显著因素：{[f[0] for f in significant_single_factors]}")
            print(f"   - 协同效应R²={r2_score:.3f}")

        elif has_significant_single and has_moderate_synergy:
            conclusion = "H2假设部分支持"
            support_level = "partial"
            print(f"   ○ {conclusion}")
            print("   - 存在显著单因素效应但多因素协同效应中等")
            print(f"   - 显著因素：{[f[0] for f in significant_single_factors]}")
            print(f"   - 协同效应R²={r2_score:.3f}，需要更多数据验证")

        elif has_significant_single:
            conclusion = "H2假设部分支持（单因素主导）"
            support_level = "single_factor"
            print(f"   ○ {conclusion}")
            print("   - 存在显著单因素效应但多因素协同效应不明确")
            print(f"   - 显著因素：{[f[0] for f in significant_single_factors]}")
            print("   - 建议重点关注单因素控制策略")

        else:
            conclusion = "H2假设证据不足"
            support_level = "insufficient"
            print(f"   ✗ {conclusion}")
            print("   - 单因素效应和多因素协同效应均不显著")
            print("   - 可能需要：(1)扩大样本量 (2)考虑其他因素 (3)改进测量精度")

        # 4. 关键发现和实际意义
        print("\n4. 关键发现和工程意义：")

        # Type 4失效样本的高度差异
        type4_samples = self.clean_data[self.clean_data['is_type4_failure'] == 1]
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]

        if len(type4_samples) > 0 and len(success_samples) > 0:
            type4_height = type4_samples['post_weld_disk_holder_height'].mean()
            success_height = success_samples['post_weld_disk_holder_height'].mean()
            height_diff = type4_height - success_height

            print(f"   - Type 4失效样本平均高度：{type4_height:.3f}mm")
            print(f"   - 成功样本平均高度：{success_height:.3f}mm")
            print(f"   - 高度差异：{height_diff:+.3f}mm")

        # 最重要的单因素
        if significant_single_factors:
            # 按p值排序，最小p值为最重要
            most_important = min(significant_single_factors, key=lambda x: x[1])
            print(f"   - 最重要单因素：{most_important[0]} (p={most_important[1]:.3f})")

            # 工程建议
            if most_important[0] == 'gas_fill_pressure':
                print("   - 工程建议：重点控制充气压力以减少Type 4失效")
            elif most_important[0] == 'electrode_pressure':
                print("   - 工程建议：优化电极压力设置")
            elif most_important[0] == 's2_hold':
                print("   - 工程建议：调整S2保持时间")
        else:
            print("   - 无显著单因素，建议扩大研究范围")

        # 5. 统计置信度和局限性
        print("\n5. 统计置信度和局限性：")
        sample_size = len(self.clean_data)
        type4_count = len(type4_samples) if 'type4_samples' in locals() else 0

        print(f"   - 总样本量：{sample_size}")
        print(f"   - Type 4失效样本：{type4_count}")
        print(f"   - 统计功效：{'充足' if sample_size >= 100 else '有限'}")

        if sample_size < 100:
            print("   - 局限性：样本量相对较小，建议扩大验证")
        if type4_count < 20:
            print("   - 局限性：Type 4失效样本较少，影响统计功效")

        # 保存重构后的结论
        self.results['h2_conclusion_v2'] = {
            'conclusion': conclusion,
            'support_level': support_level,
            'significant_single_factors': significant_single_factors,
            'marginal_single_factors': marginal_single_factors,
            'synergy_level': synergy_level,
            'r2_score': r2_score,
            'sample_limitations': {
                'total_samples': sample_size,
                'type4_samples': type4_count,
                'statistical_power': 'adequate' if sample_size >= 100 else 'limited'
            },
            'key_findings': {
                'significant_factor_count': len(significant_single_factors),
                'most_important_factor': most_important[0] if significant_single_factors else 'none',
                'height_difference': height_diff if 'height_diff' in locals() else 0.0,
                'synergy_strength': synergy_level
            }
        }

        return self.results['h2_conclusion_v2']

    def parameter_combination_analysis(self):
        """第四阶段：参数组合成功率分析"""
        print("\n" + "="*60)
        print("第四阶段：参数优化分析 - 最优参数组合识别")
        print("="*60)

        print("4.1 参数组合成功率分析：")

        # S1和S2电流组合分析
        print("\nS1和S2电流组合分析：")

        # 创建电流组合
        s1_bins = [95, 97, 98, 99, 100]
        s2_bins = [95, 97, 98, 99, 100]

        self.clean_data['s1_group'] = pd.cut(self.clean_data['s1_percent_current'],
                                           bins=s1_bins, include_lowest=True)
        self.clean_data['s2_group'] = pd.cut(self.clean_data['s2_percent_current'],
                                           bins=s2_bins, include_lowest=True)

        # 计算组合成功率
        combination_stats = self.clean_data.groupby(['s1_group', 's2_group']).agg({
            'is_success': ['count', 'sum', 'mean'],
            'is_type4_failure': 'mean',
            'is_type8_failure': 'mean'
        }).round(3)

        combination_stats.columns = ['sample_count', 'success_count', 'success_rate',
                                   'type4_rate', 'type8_rate']

        # 筛选样本量≥5的组合
        valid_combinations = combination_stats[combination_stats['sample_count'] >= 5]

        if len(valid_combinations) > 0:
            print("有效参数组合（样本量≥5）：")
            print(valid_combinations.sort_values('success_rate', ascending=False))

            # 最优组合
            best_combination = valid_combinations.loc[valid_combinations['success_rate'].idxmax()]
            print(f"\n最优电流组合：")
            print(f"S1电流区间：{best_combination.name[0]}")
            print(f"S2电流区间：{best_combination.name[1]}")
            print(f"成功率：{best_combination['success_rate']:.1%}")
            print(f"样本量：{best_combination['sample_count']:.0f}")
        else:
            print("样本量不足，无法进行可靠的组合分析")
            best_combination = None

        # S1和S2时间组合分析
        print("\n\nS1和S2时间组合分析：")

        # 创建时间组合
        s1_hold_bins = [4, 5, 6, 8]
        s2_hold_bins = [15, 18, 19, 20, 21]

        self.clean_data['s1_hold_group'] = pd.cut(self.clean_data['s1_hold'],
                                                bins=s1_hold_bins, include_lowest=True)
        self.clean_data['s2_hold_group'] = pd.cut(self.clean_data['s2_hold'],
                                                bins=s2_hold_bins, include_lowest=True)

        # 计算时间组合成功率
        time_combination_stats = self.clean_data.groupby(['s1_hold_group', 's2_hold_group']).agg({
            'is_success': ['count', 'sum', 'mean'],
            'is_type4_failure': 'mean',
            'is_type8_failure': 'mean'
        }).round(3)

        time_combination_stats.columns = ['sample_count', 'success_count', 'success_rate',
                                        'type4_rate', 'type8_rate']

        # 筛选样本量≥3的组合
        valid_time_combinations = time_combination_stats[time_combination_stats['sample_count'] >= 3]

        if len(valid_time_combinations) > 0:
            print("有效时间组合（样本量≥3）：")
            print(valid_time_combinations.sort_values('success_rate', ascending=False))

            # 最优时间组合
            best_time_combination = valid_time_combinations.loc[valid_time_combinations['success_rate'].idxmax()]
            print(f"\n最优时间组合：")
            print(f"S1时间区间：{best_time_combination.name[0]}")
            print(f"S2时间区间：{best_time_combination.name[1]}")
            print(f"成功率：{best_time_combination['success_rate']:.1%}")
            print(f"样本量：{best_time_combination['sample_count']:.0f}")
        else:
            print("时间组合样本量不足")
            best_time_combination = None

        # 保存结果
        self.results['parameter_combinations'] = {
            'current_combinations': valid_combinations if len(valid_combinations) > 0 else None,
            'time_combinations': valid_time_combinations if len(valid_time_combinations) > 0 else None,
            'best_current_combination': best_combination,
            'best_time_combination': best_time_combination
        }

        return self.results['parameter_combinations']

    def parameter_anova_analysis(self):
        """参数方差分析"""
        print("\n4.2 参数方差分析：")

        # S1电流对成功率的影响
        print("S1电流对成功率的影响：")
        s1_groups = self.clean_data.groupby('s1_group')['is_success'].agg(['count', 'mean'])
        s1_groups = s1_groups[s1_groups['count'] >= 3]  # 至少3个样本

        if len(s1_groups) > 1:
            print(s1_groups)

            # 单因素方差分析
            s1_success_by_group = [group['is_success'].values for name, group in
                                  self.clean_data.groupby('s1_group') if len(group) >= 3]

            if len(s1_success_by_group) > 1:
                f_stat_s1, p_value_s1 = stats.f_oneway(*s1_success_by_group)
                print(f"S1电流方差分析：F={f_stat_s1:.3f}, p={p_value_s1:.3f}")
            else:
                f_stat_s1, p_value_s1 = np.nan, np.nan
        else:
            f_stat_s1, p_value_s1 = np.nan, np.nan

        # S2电流对成功率的影响
        print("\nS2电流对成功率的影响：")
        s2_groups = self.clean_data.groupby('s2_group')['is_success'].agg(['count', 'mean'])
        s2_groups = s2_groups[s2_groups['count'] >= 3]

        if len(s2_groups) > 1:
            print(s2_groups)

            # 单因素方差分析
            s2_success_by_group = [group['is_success'].values for name, group in
                                  self.clean_data.groupby('s2_group') if len(group) >= 3]

            if len(s2_success_by_group) > 1:
                f_stat_s2, p_value_s2 = stats.f_oneway(*s2_success_by_group)
                print(f"S2电流方差分析：F={f_stat_s2:.3f}, p={p_value_s2:.3f}")
            else:
                f_stat_s2, p_value_s2 = np.nan, np.nan
        else:
            f_stat_s2, p_value_s2 = np.nan, np.nan

        # 电极压力分组分析
        print("\n电极压力对成功率的影响：")
        electrode_pressure_bins = [42, 43, 44, 45]
        self.clean_data['electrode_pressure_group'] = pd.cut(self.clean_data['electrode_pressure'],
                                                           bins=electrode_pressure_bins, include_lowest=True)

        electrode_groups = self.clean_data.groupby('electrode_pressure_group')['is_success'].agg(['count', 'mean'])
        electrode_groups = electrode_groups[electrode_groups['count'] >= 3]

        if len(electrode_groups) > 1:
            print(electrode_groups)

            electrode_success_by_group = [group['is_success'].values for name, group in
                                        self.clean_data.groupby('electrode_pressure_group') if len(group) >= 3]

            if len(electrode_success_by_group) > 1:
                f_stat_electrode, p_value_electrode = stats.f_oneway(*electrode_success_by_group)
                print(f"电极压力方差分析：F={f_stat_electrode:.3f}, p={p_value_electrode:.3f}")
            else:
                f_stat_electrode, p_value_electrode = np.nan, np.nan
        else:
            f_stat_electrode, p_value_electrode = np.nan, np.nan

        # 保存结果
        self.results['parameter_anova'] = {
            's1_current_f': f_stat_s1,
            's1_current_p': p_value_s1,
            's2_current_f': f_stat_s2,
            's2_current_p': p_value_s2,
            'electrode_pressure_f': f_stat_electrode,
            'electrode_pressure_p': p_value_electrode
        }

        return self.results['parameter_anova']

    def optimal_parameter_recommendation(self):
        """最优参数推荐"""
        print("\n4.3 最优参数推荐：")

        # 基于成功率的参数推荐
        print("基于成功率的参数分析：")

        # 分析各参数的最优范围
        param_recommendations = {}

        # S1电流推荐
        s1_success_by_value = self.clean_data.groupby('s1_percent_current')['is_success'].agg(['count', 'mean'])
        s1_valid = s1_success_by_value[s1_success_by_value['count'] >= 3]

        if len(s1_valid) > 0:
            best_s1 = s1_valid.loc[s1_valid['mean'].idxmax()]
            param_recommendations['s1_percent_current'] = {
                'optimal_value': s1_valid['mean'].idxmax(),
                'success_rate': best_s1['mean'],
                'sample_count': best_s1['count']
            }
            print(f"S1电流推荐：{s1_valid['mean'].idxmax()}% (成功率：{best_s1['mean']:.1%}, n={best_s1['count']:.0f})")

        # S2电流推荐
        s2_success_by_value = self.clean_data.groupby('s2_percent_current')['is_success'].agg(['count', 'mean'])
        s2_valid = s2_success_by_value[s2_success_by_value['count'] >= 3]

        if len(s2_valid) > 0:
            best_s2 = s2_valid.loc[s2_valid['mean'].idxmax()]
            param_recommendations['s2_percent_current'] = {
                'optimal_value': s2_valid['mean'].idxmax(),
                'success_rate': best_s2['mean'],
                'sample_count': best_s2['count']
            }
            print(f"S2电流推荐：{s2_valid['mean'].idxmax()}% (成功率：{best_s2['mean']:.1%}, n={best_s2['count']:.0f})")

        # S1时间推荐
        s1_hold_success = self.clean_data.groupby('s1_hold')['is_success'].agg(['count', 'mean'])
        s1_hold_valid = s1_hold_success[s1_hold_success['count'] >= 3]

        if len(s1_hold_valid) > 0:
            best_s1_hold = s1_hold_valid.loc[s1_hold_valid['mean'].idxmax()]
            param_recommendations['s1_hold'] = {
                'optimal_value': s1_hold_valid['mean'].idxmax(),
                'success_rate': best_s1_hold['mean'],
                'sample_count': best_s1_hold['count']
            }
            print(f"S1时间推荐：{s1_hold_valid['mean'].idxmax()}周期 (成功率：{best_s1_hold['mean']:.1%}, n={best_s1_hold['count']:.0f})")

        # S2时间推荐
        s2_hold_success = self.clean_data.groupby('s2_hold')['is_success'].agg(['count', 'mean'])
        s2_hold_valid = s2_hold_success[s2_hold_success['count'] >= 3]

        if len(s2_hold_valid) > 0:
            best_s2_hold = s2_hold_valid.loc[s2_hold_valid['mean'].idxmax()]
            param_recommendations['s2_hold'] = {
                'optimal_value': s2_hold_valid['mean'].idxmax(),
                'success_rate': best_s2_hold['mean'],
                'sample_count': best_s2_hold['count']
            }
            print(f"S2时间推荐：{s2_hold_valid['mean'].idxmax()}周期 (成功率：{best_s2_hold['mean']:.1%}, n={best_s2_hold['count']:.0f})")

        # 电极压力推荐
        electrode_success = self.clean_data.groupby('electrode_pressure')['is_success'].agg(['count', 'mean'])
        electrode_valid = electrode_success[electrode_success['count'] >= 3]

        if len(electrode_valid) > 0:
            best_electrode = electrode_valid.loc[electrode_valid['mean'].idxmax()]
            param_recommendations['electrode_pressure'] = {
                'optimal_value': electrode_valid['mean'].idxmax(),
                'success_rate': best_electrode['mean'],
                'sample_count': best_electrode['count']
            }
            print(f"电极压力推荐：{electrode_valid['mean'].idxmax()}PSI (成功率：{best_electrode['mean']:.1%}, n={best_electrode['count']:.0f})")

        # 综合最优参数组合
        print("\n综合最优参数组合：")
        if param_recommendations:
            print("推荐参数设置：")
            for param, info in param_recommendations.items():
                print(f"  {param}: {info['optimal_value']} (预期成功率：{info['success_rate']:.1%})")

            # 保守成功率评估（移除不可靠的简单平均预测）
            print(f"\n⚠️  成功率预测说明：")
            print("   基于当前数据，无法提供可靠的综合成功率预测")
            print("   原因：")
            print("   - 部分'最优参数'基于极小样本量（n<15）")
            print("   - 简单平均法忽略参数间交互效应")
            print("   - 缺乏统计推断的置信区间")
            print("   建议：通过验证性DOE确认参数优化效果")

        # 工艺窗口分析
        print("\n工艺窗口分析：")

        # 成功样本的参数范围
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]

        param_windows = {}
        key_params = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold', 'electrode_pressure']

        for param in key_params:
            if param in success_samples.columns:
                param_values = success_samples[param].dropna()
                if len(param_values) > 0:
                    param_windows[param] = {
                        'min': param_values.min(),
                        'max': param_values.max(),
                        'mean': param_values.mean(),
                        'std': param_values.std(),
                        'q25': param_values.quantile(0.25),
                        'q75': param_values.quantile(0.75)
                    }

                    print(f"{param}工艺窗口：")
                    print(f"  范围：{param_windows[param]['min']:.1f} - {param_windows[param]['max']:.1f}")
                    print(f"  推荐范围（25%-75%分位）：{param_windows[param]['q25']:.1f} - {param_windows[param]['q75']:.1f}")
                    print(f"  中心值：{param_windows[param]['mean']:.1f}±{param_windows[param]['std']:.1f}")

        # 保存结果
        self.results['optimal_parameters'] = {
            'parameter_recommendations': param_recommendations,
            'process_windows': param_windows,
            'estimated_success_rate': estimated_success_rate if param_recommendations else None
        }

        return self.results['optimal_parameters']

    def generate_hypothesis_verification_charts(self):
        """生成假设验证图表"""
        print("\n生成H1和H2假设验证图表...")

        # 设置matplotlib后端为非交互式
        import matplotlib
        matplotlib.use('Agg')

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建H1假设验证图表
        fig1, axes1 = plt.subplots(2, 2, figsize=(16, 12))
        fig1.suptitle('H1假设验证：高电流与Type 8裂纹失效关联性分析', fontsize=16, fontweight='bold')

        # H1-1: Type 8失效样本电流分布对比
        type8_samples = self.clean_data[self.clean_data['is_type8_failure'] == 1]
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]

        axes1[0,0].hist(success_samples['s2_percent_current'], bins=15, alpha=0.7, label='成功样本', color='green', density=True)
        axes1[0,0].hist(type8_samples['s2_percent_current'], bins=15, alpha=0.7, label='Type 8失效', color='red', density=True)
        axes1[0,0].set_title('S2电流分布对比', fontsize=12, fontweight='bold')
        axes1[0,0].set_xlabel('S2电流(%)')
        axes1[0,0].set_ylabel('密度')
        axes1[0,0].legend()
        axes1[0,0].grid(True, alpha=0.3)

        # H1-2: 电流水平与Type 8失效率关系
        current_failure_rate = self.clean_data.groupby('s2_percent_current')['is_type8_failure'].agg(['mean', 'count'])
        valid_rates = current_failure_rate[current_failure_rate['count'] >= 3]

        axes1[0,1].bar(valid_rates.index, valid_rates['mean'] * 100, alpha=0.7, color='orange', edgecolor='black')
        axes1[0,1].set_title('S2电流水平与Type 8失效率', fontsize=12, fontweight='bold')
        axes1[0,1].set_xlabel('S2电流(%)')
        axes1[0,1].set_ylabel('Type 8失效率(%)')
        axes1[0,1].grid(True, alpha=0.3)

        # H1-3: ROC曲线（如果有逻辑回归结果）
        if 'h1_logistic' in self.results:
            from sklearn.metrics import roc_curve, auc
            h1_results = self.results['h1_logistic']
            if 'y_test' in h1_results and 'y_pred_proba' in h1_results:
                fpr, tpr, _ = roc_curve(h1_results['y_test'], h1_results['y_pred_proba'])
                roc_auc = auc(fpr, tpr)

                axes1[1,0].plot(fpr, tpr, color='darkorange', lw=2, label=f'ROC曲线 (AUC = {roc_auc:.3f})')
                axes1[1,0].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
                axes1[1,0].set_xlim([0.0, 1.0])
                axes1[1,0].set_ylim([0.0, 1.05])
                axes1[1,0].set_xlabel('假正率')
                axes1[1,0].set_ylabel('真正率')
                axes1[1,0].set_title('H1假设ROC曲线', fontsize=12, fontweight='bold')
                axes1[1,0].legend(loc="lower right")
                axes1[1,0].grid(True, alpha=0.3)

        # H1-4: 电流与失效类型箱线图
        failure_current_data = []
        failure_labels = []
        for failure_type in [1, 4, 8]:
            type_data = self.clean_data[self.clean_data['weld_failure_type'] == failure_type]['s2_percent_current']
            if len(type_data) > 0:
                failure_current_data.append(type_data)
                failure_labels.append(f'Type {failure_type}')

        if failure_current_data:
            bp = axes1[1,1].boxplot(failure_current_data, labels=failure_labels, patch_artist=True)
            colors = ['green', 'orange', 'red']
            for patch, color in zip(bp['boxes'], colors[:len(bp['boxes'])]):
                patch.set_facecolor(color)
                patch.set_alpha(0.7)
            axes1[1,1].set_title('不同失效类型的S2电流分布', fontsize=12, fontweight='bold')
            axes1[1,1].set_ylabel('S2电流(%)')
            axes1[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('g3p_h1_hypothesis_verification.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 创建H2假设验证图表
        fig2, axes2 = plt.subplots(2, 2, figsize=(16, 12))
        fig2.suptitle('H2假设验证：Type 4高度失效多因素分析', fontsize=16, fontweight='bold')

        # H2-1: Type 4失效样本高度分布对比
        type4_samples = self.clean_data[self.clean_data['is_type4_failure'] == 1]

        axes2[0,0].hist(success_samples['post_weld_disk_holder_height'], bins=15, alpha=0.7,
                       label='成功样本', color='green', density=True)
        axes2[0,0].hist(type4_samples['post_weld_disk_holder_height'], bins=15, alpha=0.7,
                       label='Type 4失效', color='red', density=True)
        axes2[0,0].set_title('焊接高度分布对比', fontsize=12, fontweight='bold')
        axes2[0,0].set_xlabel('焊接高度(mm)')
        axes2[0,0].set_ylabel('密度')
        axes2[0,0].legend()
        axes2[0,0].grid(True, alpha=0.3)

        # H2-2: 充气压力与Type 4失效率关系
        pressure_bins = pd.cut(self.clean_data['gas_fill_pressure'], bins=5)
        pressure_failure_rate = self.clean_data.groupby(pressure_bins)['is_type4_failure'].agg(['mean', 'count'])
        valid_pressure_rates = pressure_failure_rate[pressure_failure_rate['count'] >= 3]

        if len(valid_pressure_rates) > 0:
            x_labels = [f'{interval.left:.0f}-{interval.right:.0f}' for interval in valid_pressure_rates.index]
            axes2[0,1].bar(range(len(valid_pressure_rates)), valid_pressure_rates['mean'] * 100,
                          alpha=0.7, color='purple', edgecolor='black')
            axes2[0,1].set_title('充气压力与Type 4失效率', fontsize=12, fontweight='bold')
            axes2[0,1].set_xlabel('充气压力区间(psi)')
            axes2[0,1].set_ylabel('Type 4失效率(%)')
            axes2[0,1].set_xticks(range(len(valid_pressure_rates)))
            axes2[0,1].set_xticklabels(x_labels, rotation=45)
            axes2[0,1].grid(True, alpha=0.3)

        # H2-3: 多因素影响热图
        if 'h2_anova' in self.results:
            factors = ['gas_fill_pressure', 's2_hold', 'electrode_pressure', 'room_temperature']
            p_values = []
            effect_sizes = []

            for factor in factors:
                if factor in self.results['h2_anova']:
                    p_values.append(self.results['h2_anova'][factor].get('p_value', 1.0))
                    effect_sizes.append(abs(self.results['h2_anova'][factor].get('effect_size', 0.0)))
                else:
                    p_values.append(1.0)
                    effect_sizes.append(0.0)

            # 创建热图数据
            heatmap_data = np.array([p_values, effect_sizes])
            im = axes2[1,0].imshow(heatmap_data, cmap='RdYlGn_r', aspect='auto')

            axes2[1,0].set_xticks(range(len(factors)))
            axes2[1,0].set_xticklabels([f.replace('_', '\n') for f in factors], rotation=45)
            axes2[1,0].set_yticks([0, 1])
            axes2[1,0].set_yticklabels(['p值', '效应大小'])
            axes2[1,0].set_title('H2多因素显著性热图', fontsize=12, fontweight='bold')

            # 添加数值标注
            for i in range(len(factors)):
                axes2[1,0].text(i, 0, f'{p_values[i]:.3f}', ha='center', va='center', fontweight='bold')
                axes2[1,0].text(i, 1, f'{effect_sizes[i]:.3f}', ha='center', va='center', fontweight='bold')

        # H2-4: 关键因素箱线图
        key_factors = ['gas_fill_pressure', 'electrode_pressure']
        for idx, factor in enumerate(key_factors):
            if factor in self.clean_data.columns:
                success_data = success_samples[factor].dropna()
                type4_data = type4_samples[factor].dropna()

                if len(success_data) > 0 and len(type4_data) > 0:
                    bp = axes2[1,1].boxplot([success_data, type4_data],
                                          labels=['成功样本', 'Type 4失效'],
                                          positions=[idx*3+1, idx*3+2],
                                          patch_artist=True)

                    colors = ['green', 'red']
                    for patch, color in zip(bp['boxes'], colors):
                        patch.set_facecolor(color)
                        patch.set_alpha(0.7)

        axes2[1,1].set_title('关键因素分布对比', fontsize=12, fontweight='bold')
        axes2[1,1].set_ylabel('参数值')
        axes2[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('g3p_h2_hypothesis_verification.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("假设验证图表已生成：")
        print("- g3p_h1_hypothesis_verification.png")
        print("- g3p_h2_hypothesis_verification.png")

        return True

    def generate_optimization_charts(self):
        """生成参数优化图表"""
        print("\n生成参数优化分析图表...")

        # 设置matplotlib后端为非交互式
        import matplotlib
        matplotlib.use('Agg')

        # 创建参数优化图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('G3P OP60工艺参数优化分析', fontsize=16, fontweight='bold')

        # 1. 最优参数组合成功率对比
        if 'optimal_parameters' in self.results and self.results['optimal_parameters']['parameter_recommendations']:
            param_rec = self.results['optimal_parameters']['parameter_recommendations']
            param_names = list(param_rec.keys())
            success_rates = [param_rec[p]['success_rate'] * 100 for p in param_names]

            bars = axes[0,0].bar(range(len(param_names)), success_rates,
                               color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                               alpha=0.8, edgecolor='black')
            axes[0,0].set_title('最优参数成功率', fontsize=12, fontweight='bold')
            axes[0,0].set_ylabel('成功率(%)')
            axes[0,0].set_xticks(range(len(param_names)))
            axes[0,0].set_xticklabels([p.replace('_', '\n') for p in param_names], rotation=45)
            axes[0,0].grid(True, alpha=0.3)
            axes[0,0].set_ylim(0, 105)

            # 添加数值标注
            for i, v in enumerate(success_rates):
                axes[0,0].text(i, v + 1, f'{v:.1f}%', ha='center', fontweight='bold')

        # 2. 工艺窗口分析
        if 'optimal_parameters' in self.results and self.results['optimal_parameters']['process_windows']:
            windows = self.results['optimal_parameters']['process_windows']
            param_names = list(windows.keys())[:5]  # 取前5个参数

            means = [windows[p]['mean'] for p in param_names]
            stds = [windows[p]['std'] for p in param_names]
            q25s = [windows[p]['q25'] for p in param_names]
            q75s = [windows[p]['q75'] for p in param_names]

            x_pos = range(len(param_names))
            axes[0,1].errorbar(x_pos, means, yerr=stds, fmt='o', capsize=5, capthick=2,
                             color='blue', markersize=8, label='均值±标准差')

            # 添加四分位数范围
            for i, (q25, q75) in enumerate(zip(q25s, q75s)):
                axes[0,1].fill_between([i-0.2, i+0.2], [q25, q25], [q75, q75],
                                     alpha=0.3, color='green', label='推荐范围' if i == 0 else "")

            axes[0,1].set_title('工艺窗口分析', fontsize=12, fontweight='bold')
            axes[0,1].set_ylabel('参数值')
            axes[0,1].set_xticks(x_pos)
            axes[0,1].set_xticklabels([p.replace('_', '\n') for p in param_names], rotation=45)
            axes[0,1].legend()
            axes[0,1].grid(True, alpha=0.3)

        # 3. 参数组合热图
        if hasattr(self, 'clean_data'):
            # 创建参数相关性矩阵
            key_params = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold', 'electrode_pressure']
            param_data = self.clean_data[key_params].corr()

            im = axes[0,2].imshow(param_data.values, cmap='coolwarm', vmin=-1, vmax=1)
            axes[0,2].set_xticks(range(len(key_params)))
            axes[0,2].set_yticks(range(len(key_params)))
            axes[0,2].set_xticklabels([p.replace('_', '\n') for p in key_params], rotation=45)
            axes[0,2].set_yticklabels([p.replace('_', '\n') for p in key_params])
            axes[0,2].set_title('参数相关性热图', fontsize=12, fontweight='bold')

            # 添加相关系数标注
            for i in range(len(key_params)):
                for j in range(len(key_params)):
                    text = axes[0,2].text(j, i, f'{param_data.iloc[i, j]:.2f}',
                                        ha="center", va="center", color="black", fontweight='bold')

        # 4. 成功率趋势分析
        current_ranges = [(95, 97), (97, 98), (98, 99), (99, 100)]
        success_rates_by_range = []

        for low, high in current_ranges:
            mask = (self.clean_data['s2_percent_current'] >= low) & (self.clean_data['s2_percent_current'] < high)
            if mask.sum() > 0:
                success_rate = self.clean_data[mask]['is_success'].mean() * 100
                success_rates_by_range.append(success_rate)
            else:
                success_rates_by_range.append(0)

        range_labels = [f'{low}-{high}%' for low, high in current_ranges]
        bars = axes[1,0].bar(range_labels, success_rates_by_range,
                           color=['#90EE90', '#FFD700', '#FFA500', '#FF6347'],
                           alpha=0.8, edgecolor='black')
        axes[1,0].set_title('S2电流范围与成功率', fontsize=12, fontweight='bold')
        axes[1,0].set_xlabel('S2电流范围')
        axes[1,0].set_ylabel('成功率(%)')
        axes[1,0].grid(True, alpha=0.3)

        # 添加数值标注
        for i, v in enumerate(success_rates_by_range):
            if v > 0:
                axes[1,0].text(i, v + 1, f'{v:.1f}%', ha='center', fontweight='bold')

        # 5. 失效模式分析
        failure_by_current = self.clean_data.groupby('s2_percent_current').agg({
            'is_type4_failure': 'mean',
            'is_type8_failure': 'mean'
        }) * 100

        valid_failure_data = failure_by_current[
            (failure_by_current.index.isin(self.clean_data['s2_percent_current'].value_counts()[
                self.clean_data['s2_percent_current'].value_counts() >= 3].index))
        ]

        if len(valid_failure_data) > 0:
            x_pos = range(len(valid_failure_data))
            width = 0.35

            bars1 = axes[1,1].bar([x - width/2 for x in x_pos], valid_failure_data['is_type4_failure'],
                                width, label='Type 4失效率', color='orange', alpha=0.8)
            bars2 = axes[1,1].bar([x + width/2 for x in x_pos], valid_failure_data['is_type8_failure'],
                                width, label='Type 8失效率', color='red', alpha=0.8)

            axes[1,1].set_title('电流水平与失效模式', fontsize=12, fontweight='bold')
            axes[1,1].set_xlabel('S2电流(%)')
            axes[1,1].set_ylabel('失效率(%)')
            axes[1,1].set_xticks(x_pos)
            axes[1,1].set_xticklabels([f'{int(idx)}%' for idx in valid_failure_data.index])
            axes[1,1].legend()
            axes[1,1].grid(True, alpha=0.3)

        # 6. 改进潜力分析
        current_success = self.results['descriptive']['success_rate']
        if 'optimal_parameters' in self.results and self.results['optimal_parameters']['estimated_success_rate']:
            estimated_success = self.results['optimal_parameters']['estimated_success_rate'] * 100
        else:
            estimated_success = current_success

        improvement = estimated_success - current_success

        categories = ['当前成功率', '预期成功率', '改进潜力']
        values = [current_success, estimated_success, improvement]
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']

        bars = axes[1,2].bar(categories, values, color=colors, alpha=0.8, edgecolor='black')
        axes[1,2].set_title('工艺改进潜力分析', fontsize=12, fontweight='bold')
        axes[1,2].set_ylabel('成功率(%)')
        axes[1,2].grid(True, alpha=0.3)

        # 添加数值标注
        for i, v in enumerate(values):
            axes[1,2].text(i, v + 1, f'{v:.1f}%', ha='center', fontweight='bold')

        plt.tight_layout()
        plt.savefig('g3p_parameter_optimization.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("参数优化图表已生成：g3p_parameter_optimization.png")

        return True

    def parameter_optimization_v2(self):
        """参数优化分析 - 重构版本：严格统计标准"""
        print("\n" + "="*60)
        print("参数优化分析 (重构版本) - 严格统计标准")
        print("="*60)

        # 设置严格的最小样本量标准
        MIN_SAMPLE_SIZE = 15
        CONFIDENCE_LEVEL = 0.95

        print(f"统计标准：")
        print(f"- 最小样本量要求：n ≥ {MIN_SAMPLE_SIZE}")
        print(f"- 置信水平：{CONFIDENCE_LEVEL*100}%")
        print(f"- 效应大小阈值：Cohen's d ≥ 0.5")

        # 分析成功样本的参数分布
        success_samples = self.clean_data[self.clean_data['is_success'] == 1]
        total_success = len(success_samples)

        print(f"\n成功样本总数：{total_success}")

        # 关键参数列表
        key_params = {
            's1_percent_current': 'S1电流',
            's2_percent_current': 'S2电流',
            's1_hold': 'S1时间',
            's2_hold': 'S2时间',
            'electrode_pressure': '电极压力'
        }

        reliable_recommendations = {}
        unreliable_findings = {}

        print("\n参数优化分析结果：")

        for param_key, param_name in key_params.items():
            if param_key not in success_samples.columns:
                continue

            param_values = success_samples[param_key].dropna()
            if len(param_values) == 0:
                continue

            print(f"\n{param_name} ({param_key}):")

            # 计算基础统计量
            mean_val = param_values.mean()
            std_val = param_values.std()
            median_val = param_values.median()
            q25 = param_values.quantile(0.25)
            q75 = param_values.quantile(0.75)

            print(f"  基础统计：均值={mean_val:.2f}, 中位数={median_val:.2f}, 标准差={std_val:.2f}")
            print(f"  四分位数：Q1={q25:.2f}, Q3={q75:.2f}")

            # 寻找高成功率区间
            # 使用分位数方法创建区间
            try:
                # 创建5个等频区间
                param_bins = pd.qcut(param_values, q=5, duplicates='drop')
                bin_analysis = param_values.groupby(param_bins).agg(['count', 'mean']).round(3)

                print(f"  区间分析：")
                for interval, stats in bin_analysis.iterrows():
                    count = stats[('count',)]
                    if count >= MIN_SAMPLE_SIZE:
                        # 计算置信区间（假设正态分布）
                        from scipy import stats as scipy_stats
                        interval_data = param_values[param_bins == interval]
                        ci_lower, ci_upper = scipy_stats.t.interval(
                            CONFIDENCE_LEVEL,
                            len(interval_data)-1,
                            loc=interval_data.mean(),
                            scale=scipy_stats.sem(interval_data)
                        )

                        print(f"    {interval}: n={count:.0f} (可靠)")
                        print(f"      95%置信区间: [{ci_lower:.2f}, {ci_upper:.2f}]")

                        # 保存可靠推荐
                        if param_key not in reliable_recommendations:
                            reliable_recommendations[param_key] = {
                                'param_name': param_name,
                                'recommended_range': (float(interval.left), float(interval.right)),
                                'sample_size': int(count),
                                'confidence_interval': (ci_lower, ci_upper),
                                'mean_value': interval_data.mean()
                            }
                    else:
                        print(f"    {interval}: n={count:.0f} (样本量不足)")

                        # 记录不可靠发现
                        if param_key not in unreliable_findings:
                            unreliable_findings[param_key] = []
                        unreliable_findings[param_key].append({
                            'range': (float(interval.left), float(interval.right)),
                            'sample_size': int(count),
                            'reason': 'insufficient_sample_size'
                        })

            except Exception as e:
                print(f"    区间分析失败：{str(e)}")
                continue

        # 总结可靠推荐
        print(f"\n" + "="*50)
        print("可靠参数推荐 (n≥15, 95%置信区间)")
        print("="*50)

        if reliable_recommendations:
            for param_key, rec in reliable_recommendations.items():
                print(f"\n✓ {rec['param_name']}:")
                print(f"  推荐范围：{rec['recommended_range'][0]:.2f} - {rec['recommended_range'][1]:.2f}")
                print(f"  样本量：{rec['sample_size']}")
                print(f"  95%置信区间：[{rec['confidence_interval'][0]:.2f}, {rec['confidence_interval'][1]:.2f}]")
                print(f"  平均值：{rec['mean_value']:.2f}")
        else:
            print("⚠️  无参数满足严格统计标准 (n≥15)")

        # 总结不可靠发现
        print(f"\n" + "="*50)
        print("不可靠发现 (样本量不足)")
        print("="*50)

        if unreliable_findings:
            for param_key, findings in unreliable_findings.items():
                param_name = key_params[param_key]
                print(f"\n⚠️  {param_name}:")
                for finding in findings:
                    print(f"  范围：{finding['range'][0]:.2f} - {finding['range'][1]:.2f} (n={finding['sample_size']})")
                print(f"  建议：需要更多数据验证")

        # 综合建议
        print(f"\n" + "="*50)
        print("综合优化建议")
        print("="*50)

        if reliable_recommendations:
            print("✓ 基于可靠统计证据的建议：")
            for param_key, rec in reliable_recommendations.items():
                print(f"  - {rec['param_name']}：控制在{rec['recommended_range'][0]:.1f}-{rec['recommended_range'][1]:.1f}范围内")

        print("\n⚠️  重要说明：")
        print("- 当前数据集中多数参数区间样本量不足")
        print("- 建议通过验证性DOE扩大关键参数的样本量")
        print("- 避免基于小样本做出重大工艺调整")

        print("\n📋 后续行动建议：")
        print("1. 设计针对性DOE验证可靠推荐参数")
        print("2. 重点收集样本量不足区间的数据")
        print("3. 建立参数交互效应模型")
        print("4. 实施渐进式工艺优化策略")

        # 保存结果
        self.results['parameter_optimization_v2'] = {
            'reliable_recommendations': reliable_recommendations,
            'unreliable_findings': unreliable_findings,
            'statistical_standards': {
                'min_sample_size': MIN_SAMPLE_SIZE,
                'confidence_level': CONFIDENCE_LEVEL,
                'effect_size_threshold': 0.5
            },
            'summary': {
                'reliable_param_count': len(reliable_recommendations),
                'total_params_analyzed': len(key_params),
                'recommendation_reliability': 'high' if len(reliable_recommendations) >= 3 else 'moderate' if len(reliable_recommendations) >= 1 else 'low'
            }
        }

        return self.results['parameter_optimization_v2']

    def build_prediction_models_v2(self):
        """预测模型建立 - 重构版本：承认性能限制，降级为探索性工具"""
        print("\n" + "="*60)
        print("预测模型建立 (重构版本) - 探索性分析工具")
        print("="*60)

        print("⚠️  重要说明：")
        print("本模型性能有限（AUC≈0.596，接近随机猜测），")
        print("仅作为探索性分析工具，不建议用于生产决策。")
        print("="*60)

        # 准备特征
        feature_columns = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold',
                          'electrode_pressure', 'gas_fill_pressure', 'room_temperature']

        # 检查特征可用性
        available_features = [col for col in feature_columns if col in self.clean_data.columns]
        print(f"可用特征：{len(available_features)}/{len(feature_columns)}")

        if len(available_features) < 3:
            print("❌ 可用特征不足，无法建立有效模型")
            return None

        # 准备数据
        X = self.clean_data[available_features].fillna(self.clean_data[available_features].median())
        y = self.clean_data['is_success']

        print(f"训练样本：{len(X)}")
        print(f"成功率：{y.mean():.1%}")

        # 分割数据
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42, stratify=y)

        # 建立逻辑回归模型
        from sklearn.linear_model import LogisticRegression
        from sklearn.preprocessing import StandardScaler
        from sklearn.metrics import classification_report, roc_auc_score, confusion_matrix
        from sklearn.model_selection import cross_val_score

        # 标准化特征
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        # 训练模型
        model = LogisticRegression(random_state=42, max_iter=1000)
        model.fit(X_train_scaled, y_train)

        # 预测
        y_pred = model.predict(X_test_scaled)
        y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]

        # 评估模型性能
        auc_score = roc_auc_score(y_test, y_pred_proba)
        cv_scores = cross_val_score(model, X_train_scaled, y_train, cv=5, scoring='accuracy')

        print(f"\n模型性能评估：")
        print(f"- AUC得分：{auc_score:.3f}")
        print(f"- 交叉验证准确率：{cv_scores.mean():.3f} ± {cv_scores.std():.3f}")

        # 性能评级
        if auc_score >= 0.8:
            performance_grade = "优秀"
            reliability = "高"
        elif auc_score >= 0.7:
            performance_grade = "良好"
            reliability = "中等"
        elif auc_score >= 0.6:
            performance_grade = "可接受"
            reliability = "低"
        else:
            performance_grade = "差"
            reliability = "极低"

        print(f"- 性能评级：{performance_grade}")
        print(f"- 可靠性：{reliability}")

        # 特征重要性分析（带警告）
        feature_importance = pd.DataFrame({
            'feature': available_features,
            'coefficient': model.coef_[0],
            'abs_coefficient': np.abs(model.coef_[0])
        }).sort_values('abs_coefficient', ascending=False)

        print(f"\n特征重要性分析（⚠️ 基于弱分类器，仅供参考）：")
        if auc_score < 0.7:
            print("❌ 警告：模型性能不佳，特征重要性排序可信度低")
            print("建议：不要基于此排序做出工程决策")

        for _, row in feature_importance.head(5).iterrows():
            direction = "正向" if row['coefficient'] > 0 else "负向"
            print(f"  {row['feature']}: 系数={row['coefficient']:.4f} ({direction}影响)")

        # 混淆矩阵
        cm = confusion_matrix(y_test, y_pred)
        print(f"\n混淆矩阵：")
        print(f"  预测\\实际    失效    成功")
        print(f"  失效        {cm[0,0]:3d}    {cm[0,1]:3d}")
        print(f"  成功        {cm[1,0]:3d}    {cm[1,1]:3d}")

        # 模型应用建议
        print(f"\n" + "="*50)
        print("模型应用建议")
        print("="*50)

        if auc_score >= 0.7:
            print("✓ 模型可用于：")
            print("  - 参数趋势分析")
            print("  - 相对重要性评估")
            print("  - 工艺改进方向指导")
        else:
            print("❌ 模型不建议用于：")
            print("  - 生产质量预测")
            print("  - 确定性工艺决策")
            print("  - 精确成功率估算")

        print("\n✓ 模型适用场景：")
        print("  - 探索性数据分析")
        print("  - 假设生成")
        print("  - 趋势识别")

        print("\n⚠️  使用限制：")
        print("  - 预测准确性有限")
        print("  - 特征重要性仅供参考")
        print("  - 需要更多数据改进模型")

        # 改进建议
        print(f"\n" + "="*50)
        print("模型改进建议")
        print("="*50)
        print("1. 数据质量改进：")
        print("   - 扩大样本量（目标：>300样本）")
        print("   - 平衡失效类型分布")
        print("   - 增加特征工程")

        print("2. 模型方法改进：")
        print("   - 尝试集成学习方法")
        print("   - 考虑非线性模型")
        print("   - 处理类别不平衡")

        print("3. 验证策略：")
        print("   - 时间序列验证")
        print("   - 外部数据集验证")
        print("   - A/B测试验证")

        # 保存结果
        self.results['prediction_models_v2'] = {
            'model_performance': {
                'auc_score': auc_score,
                'cv_accuracy_mean': cv_scores.mean(),
                'cv_accuracy_std': cv_scores.std(),
                'performance_grade': performance_grade,
                'reliability': reliability
            },
            'feature_importance': feature_importance.to_dict('records'),
            'confusion_matrix': cm.tolist(),
            'model_limitations': {
                'poor_discriminative_ability': auc_score < 0.7,
                'feature_ranking_unreliable': auc_score < 0.7,
                'not_suitable_for_production': auc_score < 0.8
            },
            'recommendations': {
                'use_for_exploration': True,
                'use_for_production': False,
                'improvement_needed': True
            }
        }

        return self.results['prediction_models_v2']

    def build_prediction_models(self):
        """第五阶段：预测模型建立"""
        print("\n" + "="*60)
        print("第五阶段：预测模型建立")
        print("="*60)

        print("5.1 特征工程：")

        # 准备特征
        feature_columns = ['s1_percent_current', 's2_percent_current', 's1_hold', 's2_hold',
                          'electrode_pressure', 'gas_fill_pressure', 'room_temperature']

        X = self.clean_data[feature_columns].copy()
        y_success = self.clean_data['is_success']
        y_type4 = self.clean_data['is_type4_failure']
        y_type8 = self.clean_data['is_type8_failure']

        # 处理缺失值
        X = X.fillna(X.mean())

        # 特征标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)

        print(f"特征数量：{X.shape[1]}")
        print(f"样本数量：{X.shape[0]}")
        print("特征列表：", feature_columns)

        # 数据分割
        X_train, X_test, y_success_train, y_success_test = train_test_split(
            X_scaled, y_success, test_size=0.2, random_state=42, stratify=y_success)

        print(f"训练集：{X_train.shape[0]}样本")
        print(f"测试集：{X_test.shape[0]}样本")

        print("\n5.2 模型训练与比较：")

        # 模型字典
        models = {
            'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42),
            'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000)
        }

        model_results = {}

        for model_name, model in models.items():
            print(f"\n{model_name}模型：")

            # 训练模型
            model.fit(X_train, y_success_train)

            # 预测
            y_pred_train = model.predict(X_train)
            y_pred_test = model.predict(X_test)
            y_pred_proba_test = model.predict_proba(X_test)[:, 1]

            # 评估指标
            train_accuracy = model.score(X_train, y_success_train)
            test_accuracy = model.score(X_test, y_success_test)

            # 交叉验证
            cv_scores = cross_val_score(model, X_scaled, y_success, cv=5)

            # AUC
            if len(np.unique(y_success_test)) > 1:
                auc_score = roc_auc_score(y_success_test, y_pred_proba_test)
            else:
                auc_score = np.nan

            print(f"  训练准确率：{train_accuracy:.3f}")
            print(f"  测试准确率：{test_accuracy:.3f}")
            print(f"  交叉验证准确率：{cv_scores.mean():.3f}±{cv_scores.std():.3f}")
            print(f"  AUC：{auc_score:.3f}")

            # 特征重要性（如果支持）
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': feature_columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)

                print("  特征重要性：")
                for _, row in feature_importance.head(3).iterrows():
                    print(f"    {row['feature']}: {row['importance']:.3f}")

            # 保存结果
            model_results[model_name] = {
                'model': model,
                'train_accuracy': train_accuracy,
                'test_accuracy': test_accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'auc_score': auc_score,
                'feature_importance': feature_importance if hasattr(model, 'feature_importances_') else None
            }

        # 选择最佳模型
        best_model_name = max(model_results.keys(),
                             key=lambda x: model_results[x]['cv_mean'])
        best_model_info = model_results[best_model_name]

        print(f"\n最佳模型：{best_model_name}")
        print(f"交叉验证准确率：{best_model_info['cv_mean']:.3f}")
        print(f"AUC：{best_model_info['auc_score']:.3f}")

        # 保存结果
        self.results['prediction_models'] = {
            'feature_columns': feature_columns,
            'scaler': scaler,
            'model_results': model_results,
            'best_model_name': best_model_name,
            'best_model': best_model_info['model']
        }

        return self.results['prediction_models']

    def final_summary_and_recommendations(self):
        """第六阶段：结果整合与建议"""
        print("\n" + "="*60)
        print("第六阶段：结果整合与工艺优化建议")
        print("="*60)

        print("6.1 假设验证总结：")

        # H1假设结论
        h1_conclusion = self.results['h1_conclusion']
        print(f"H1假设（99%电流与Type 8裂纹失效相关性）：{h1_conclusion['conclusion']}")
        if h1_conclusion['hypothesis_supported']:
            print("  ✓ 高电流确实增加裂纹风险")
            print(f"  ✓ Type 8失效样本S2电流平均高{h1_conclusion['key_findings']['s2_current_diff']:.1f}%")
        else:
            print("  - 电流与裂纹失效关联性较弱")

        # H2假设结论
        h2_conclusion = self.results['h2_conclusion']
        print(f"\nH2假设（Type 4高度失效多因素驱动）：{h2_conclusion['conclusion']}")
        if h2_conclusion['hypothesis_supported']:
            print("  ✓ 多因素对高度失效有显著影响")
            print(f"  ✓ 显著因子数量：{h2_conclusion['key_findings']['significant_factor_count']}")
        else:
            print("  - 多因素影响未达到统计显著水平")
            print(f"  - 但{h2_conclusion['key_findings']['most_important_factor']}显示最大影响")

        print(f"  - Type 4失效样本平均高度高{h2_conclusion['key_findings']['height_difference']:+.3f}mm")

        print("\n6.2 关键发现总结：")

        # 当前工艺状态
        desc_results = self.results['descriptive']
        print(f"当前工艺状态：")
        print(f"  - 总体成功率：{desc_results['success_rate']:.1f}%")
        print(f"  - Type 4失效率：{desc_results['type4_rate']:.1f}%")
        print(f"  - Type 8失效率：{desc_results['type8_rate']:.1f}%")

        # 最优参数
        optimal_params = self.results['optimal_parameters']
        print(f"\n最优参数识别：")
        if optimal_params['parameter_recommendations']:
            for param, info in optimal_params['parameter_recommendations'].items():
                print(f"  - {param}: {info['optimal_value']} (成功率：{info['success_rate']:.1%})")
            print(f"  - 估算综合成功率：{optimal_params['estimated_success_rate']:.1%}")

        # 预测模型性能
        model_results = self.results['prediction_models']
        best_model = model_results['best_model_name']
        best_performance = model_results['model_results'][best_model]
        print(f"\n预测模型性能：")
        print(f"  - 最佳模型：{best_model}")
        print(f"  - 交叉验证准确率：{best_performance['cv_mean']:.3f}")
        print(f"  - AUC：{best_performance['auc_score']:.3f}")

        print("\n6.3 工艺优化建议：")

        print("立即实施建议：")

        # 基于H1假设的建议
        if h1_conclusion['hypothesis_supported']:
            print("  1. 电流控制优化：")
            print("     - 控制S2电流≤98%以降低裂纹风险")
            print("     - 避免使用S2=99%参数组合")
            print("     - 优化电流曲线，避免过高峰值电流")

        # 基于H2假设的建议
        if len(h2_conclusion['significant_factors']) > 0:
            print("  2. 多参数协同控制：")
            for factor in h2_conclusion['significant_factors']:
                if factor == 'gas_fill_pressure':
                    print("     - 适当降低充气压力以控制焊接高度")
                elif factor == 's2_hold':
                    print("     - 优化S2保持时间以改善高度控制")
                elif factor == 'electrode_pressure':
                    print("     - 提高电极压力以改善焊接质量")

        # 基于最优参数的建议
        print("  3. 推荐参数设置：")
        if optimal_params['parameter_recommendations']:
            for param, info in optimal_params['parameter_recommendations'].items():
                if param == 's1_percent_current':
                    print(f"     - S1电流：{info['optimal_value']}%")
                elif param == 's2_percent_current':
                    print(f"     - S2电流：{info['optimal_value']}%")
                elif param == 's1_hold':
                    print(f"     - S1时间：{info['optimal_value']}周期")
                elif param == 's2_hold':
                    print(f"     - S2时间：{info['optimal_value']}周期")
                elif param == 'electrode_pressure':
                    print(f"     - 电极压力：{info['optimal_value']}PSI")

        # 工艺窗口建议
        print("  4. 工艺窗口控制：")
        if optimal_params['process_windows']:
            for param, window in optimal_params['process_windows'].items():
                if param in ['s1_percent_current', 's2_percent_current']:
                    print(f"     - {param}: {window['q25']:.1f}-{window['q75']:.1f}% (推荐范围)")
                elif param in ['s1_hold', 's2_hold']:
                    print(f"     - {param}: {window['q25']:.1f}-{window['q75']:.1f}周期 (推荐范围)")
                elif param == 'electrode_pressure':
                    print(f"     - {param}: {window['q25']:.1f}-{window['q75']:.1f}PSI (推荐范围)")

        print("\n中期改进建议：")
        print("  1. 建立在线监控系统：")
        print("     - 实时监控焊接高度")
        print("     - 在线检测裂纹形成")
        print("     - 自动调整工艺参数")

        print("  2. 深入机理研究：")
        print("     - 金相分析验证裂纹机理")
        print("     - 热循环分析优化温度控制")
        print("     - 气体流动分析改善密封效果")

        print("  3. DOE优化策略：")
        print("     - 基于当前发现设计新一轮DOE")
        print("     - 重点验证推荐参数组合")
        print("     - 扩大工艺窗口验证范围")

        print("\n6.4 预期改进效果：")
        current_success = desc_results['success_rate']
        estimated_success = optimal_params['estimated_success_rate'] if optimal_params['estimated_success_rate'] else current_success
        improvement = estimated_success - current_success

        print(f"  - 当前成功率：{current_success:.1f}%")
        print(f"  - 预期成功率：{estimated_success:.1f}%")
        print(f"  - 预期改进：{improvement:+.1f}个百分点")

        if improvement > 5:
            print("  ✓ 显著改进潜力")
        elif improvement > 0:
            print("  ○ 适度改进潜力")
        else:
            print("  - 需要进一步优化策略")

        # 风险评估
        print("\n6.5 实施风险评估：")
        print("  低风险改进：")
        print("     - 电流参数微调（±2%范围内）")
        print("     - 时间参数优化（±1周期范围内）")
        print("     - 电极压力调整（±1PSI范围内）")

        print("  中风险改进：")
        print("     - 充气压力大幅调整")
        print("     - 工艺程序重大修改")
        print("     - 多参数同时变更")

        print("  建议实施顺序：")
        print("     1. 先实施低风险改进")
        print("     2. 验证效果后再进行中风险改进")
        print("     3. 建立回退机制")

        print("\n" + "="*60)
        print("G3P OP60电阻焊接工艺统计分析完成")
        print("="*60)

        return True

# 执行完整分析
if __name__ == "__main__":
    # 初始化分析
    analyzer = G3PStatisticalAnalysis('NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv')

    # 执行第一阶段：数据准备与探索
    analyzer.load_and_inspect_data()
    analyzer.clean_and_preprocess_data()
    analyzer.descriptive_analysis()
    analyzer.data_visualization()  # 重新启用图表生成
    analyzer.stage1_summary()

    # 执行第二阶段：H1假设验证
    analyzer.h1_descriptive_analysis()
    analyzer.h1_correlation_analysis()
    analyzer.h1_chi_square_test()
    analyzer.h1_logistic_regression()
    analyzer.h1_hypothesis_conclusion()

    # 执行第三阶段：H2假设验证
    analyzer.h2_multifactor_anova()
    analyzer.h2_multiple_regression()
    analyzer.h2_decision_tree_analysis()
    analyzer.h2_hypothesis_conclusion()

    # 执行第四阶段：参数优化分析
    analyzer.parameter_combination_analysis()
    analyzer.parameter_anova_analysis()
    analyzer.optimal_parameter_recommendation()

    # 生成专业图表
    analyzer.generate_hypothesis_verification_charts()
    analyzer.generate_optimization_charts()

    # 执行第五阶段：预测模型建立
    analyzer.build_prediction_models()

    # 执行第六阶段：结果整合与建议
    analyzer.final_summary_and_recommendations()

# 重构版本的完整分析方法
def run_corrected_analysis():
    """运行修正后的完整分析"""
    print("="*80)
    print("G3P OP60电阻焊接工艺统计分析 - 重构版本")
    print("修正版本：解决H2逻辑矛盾、移除不可靠预测、严格统计标准")
    print("="*80)

    # 初始化分析器
    analyzer = G3PStatisticalAnalysis('NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv')

    try:
        # 第一阶段：数据准备与探索分析（保留原版本）
        print("\n🔍 第一阶段：数据准备与探索分析")
        analyzer.load_and_inspect_data()
        analyzer.clean_and_preprocess_data()
        analyzer.descriptive_analysis()
        analyzer.data_visualization()
        analyzer.stage1_summary()

        # 第二阶段：H1假设验证（保留原版本）
        print("\n🧪 第二阶段：H1假设验证")
        analyzer.h1_descriptive_analysis()
        analyzer.h1_correlation_analysis()
        analyzer.h1_chi_square_test()
        analyzer.h1_logistic_regression()
        analyzer.h1_hypothesis_conclusion()

        # 第三阶段：H2假设验证（使用重构版本）
        print("\n🧪 第三阶段：H2假设验证 - 重构版本")
        analyzer.h2_multifactor_anova()  # 原版本用于数据收集
        analyzer.h2_multiple_regression()
        analyzer.h2_decision_tree_analysis()
        analyzer.h2_hypothesis_conclusion_v2()  # 重构版本用于结论

        # 第四阶段：参数优化分析（使用重构版本）
        print("\n⚙️  第四阶段：参数优化分析 - 重构版本")
        analyzer.parameter_combination_analysis()  # 保留基础分析
        analyzer.parameter_optimization_v2()  # 使用严格标准的重构版本

        # 第五阶段：预测模型建立（使用重构版本）
        print("\n🤖 第五阶段：预测模型建立 - 重构版本")
        analyzer.build_prediction_models_v2()  # 使用降级版本

        # 第六阶段：结果整合与报告生成
        print("\n📊 第六阶段：结果整合与报告生成 - 重构版本")
        generate_corrected_summary(analyzer)

        print("\n" + "="*80)
        print("✅ 重构版本分析完成")
        print("主要改进：")
        print("- ✓ 解决H2假设逻辑矛盾")
        print("- ✓ 移除96.6%不可靠预测")
        print("- ✓ 实施严格参数优化标准")
        print("- ✓ 降级预测模型应用")
        print("- ✓ 提供保守可靠结论")
        print("="*80)

        return analyzer

    except Exception as e:
        print(f"❌ 重构版本分析过程中出现错误：{str(e)}")
        import traceback
        traceback.print_exc()
        return None

def generate_corrected_summary(analyzer):
    """生成修正后的综合报告"""
    print("\n" + "="*60)
    print("综合报告生成 - 重构版本")
    print("="*60)

    # 获取重构后的结果
    h2_results_v2 = analyzer.results.get('h2_conclusion_v2', {})
    param_results_v2 = analyzer.results.get('parameter_optimization_v2', {})
    model_results_v2 = analyzer.results.get('prediction_models_v2', {})

    print("📋 重构版本分析总结：")
    print("\n1. H2假设验证结论（重构）：")
    if h2_results_v2:
        print(f"   - 结论：{h2_results_v2.get('conclusion', 'N/A')}")
        print(f"   - 支持水平：{h2_results_v2.get('support_level', 'N/A')}")
        print(f"   - 显著单因素数量：{h2_results_v2.get('key_findings', {}).get('significant_factor_count', 0)}")
        print(f"   - 协同效应强度：{h2_results_v2.get('synergy_level', 'N/A')}")

    print("\n2. 参数优化建议（重构）：")
    if param_results_v2:
        reliable_count = param_results_v2.get('summary', {}).get('reliable_param_count', 0)
        print(f"   - 可靠参数推荐数量：{reliable_count}")
        print(f"   - 推荐可靠性：{param_results_v2.get('summary', {}).get('recommendation_reliability', 'N/A')}")
        if reliable_count > 0:
            print("   - 可靠推荐参数：")
            for param, rec in param_results_v2.get('reliable_recommendations', {}).items():
                print(f"     * {rec['param_name']}：{rec['recommended_range'][0]:.1f}-{rec['recommended_range'][1]:.1f}")
        else:
            print("   - ⚠️  无参数满足严格统计标准")

    print("\n3. 预测模型评估（重构）：")
    if model_results_v2:
        performance = model_results_v2.get('model_performance', {})
        print(f"   - AUC得分：{performance.get('auc_score', 'N/A'):.3f}")
        print(f"   - 性能评级：{performance.get('performance_grade', 'N/A')}")
        print(f"   - 可靠性：{performance.get('reliability', 'N/A')}")

        limitations = model_results_v2.get('model_limitations', {})
        if limitations.get('poor_discriminative_ability'):
            print("   - ⚠️  判别能力差，不建议用于生产决策")
        if limitations.get('feature_ranking_unreliable'):
            print("   - ⚠️  特征重要性排序不可靠")

    print("\n4. 关键修正说明：")
    print("   ✓ H2假设：分离单因素与多因素效应，避免逻辑矛盾")
    print("   ✓ 成功率预测：移除不可靠的96.6%预测")
    print("   ✓ 参数优化：实施n≥15最小样本量标准")
    print("   ✓ 预测模型：降级为探索性工具")

    print("\n5. 可信结论提取：")
    print("   高可信度（≥60%）：")
    print("   - 基础描述性统计（81.4%成功率等）")
    print("   - H1假设验证（电流-裂纹关联）")
    if h2_results_v2.get('key_findings', {}).get('significant_factor_count', 0) > 0:
        print("   - H2假设部分发现（单因素显著效应）")

    print("\n   中等可信度（30-60%）：")
    print("   - 数据探索分析结果")
    if h2_results_v2.get('synergy_level') in ['中等', '弱']:
        print("   - H2假设协同效应（需要更多验证）")

    print("\n   低可信度（<30%）：")
    print("   - 基于小样本的参数推荐")
    print("   - 预测模型的特征重要性排序")
    print("   - 任何具体的成功率提升预测")

    print("\n6. 后续行动建议：")
    print("   立即行动：")
    print("   - 停止使用原报告的96.6%预测进行决策")
    print("   - 基于H2单因素发现调整工艺参数")

    print("\n   中期规划：")
    print("   - 设计验证性DOE扩大样本量")
    print("   - 重点验证显著单因素效应")
    print("   - 建立更robust的预测模型")

    print("\n   长期目标：")
    print("   - 建立参数交互效应模型")
    print("   - 开发实时质量监控系统")
    print("   - 实现基于数据的工艺优化")

    return True
