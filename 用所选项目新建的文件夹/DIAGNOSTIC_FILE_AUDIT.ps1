# G3P OP60项目文件重组诊断审计脚本
# 日期: 2025-08-03
# 目的: 全面诊断文件重组过程中的问题并提供修复方案

Write-Host "=== G3P OP60项目文件重组诊断审计 ===" -ForegroundColor Red
Write-Host "日期: 2025-08-03" -ForegroundColor Yellow
Write-Host "开始诊断分析..." -ForegroundColor Yellow

# 设置错误处理
$ErrorActionPreference = "Continue"

# 创建诊断报告
$diagnosticReport = @()
$currentTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

function Add-DiagnosticEntry {
    param(
        [string]$Category,
        [string]$Item,
        [string]$ExpectedLocation,
        [string]$ActualLocation,
        [string]$Status,
        [string]$Issue
    )
    
    $script:diagnosticReport += [PSCustomObject]@{
        Category = $Category
        Item = $Item
        ExpectedLocation = $ExpectedLocation
        ActualLocation = $ActualLocation
        Status = $Status
        Issue = $Issue
        CheckTime = $currentTime
    }
}

function Test-FileLocation {
    param(
        [string]$FilePath,
        [string]$Description
    )
    
    if (Test-Path $FilePath) {
        $fileInfo = Get-Item $FilePath
        Write-Host "✅ 找到: $Description" -ForegroundColor Green
        Write-Host "   路径: $FilePath" -ForegroundColor Gray
        Write-Host "   大小: $($fileInfo.Length) 字节" -ForegroundColor Gray
        Write-Host "   修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Gray
        return $true
    }
    else {
        Write-Host "❌ 未找到: $Description" -ForegroundColor Red
        Write-Host "   预期路径: $FilePath" -ForegroundColor Gray
        return $false
    }
}

function Test-iCloudStatus {
    param([string]$FilePath)
    
    if (Test-Path $FilePath) {
        $item = Get-Item $FilePath
        $attributes = $item.Attributes
        
        # 检查iCloud状态属性
        if ($attributes -band [System.IO.FileAttributes]::ReparsePoint) {
            return "iCloud占位符"
        }
        elseif ($attributes -band [System.IO.FileAttributes]::Offline) {
            return "iCloud离线"
        }
        else {
            return "本地可用"
        }
    }
    return "文件不存在"
}

Write-Host "`n=== 第一部分: iCloud Drive状态检查 ===" -ForegroundColor Cyan

# 检查iCloud Drive状态
$icloudPath = $PWD.Path
Write-Host "当前工作目录: $icloudPath" -ForegroundColor Yellow

if ($icloudPath -like "*iCloudDrive*") {
    Write-Host "⚠️  检测到iCloud Drive环境" -ForegroundColor Yellow
    Write-Host "这可能导致文件操作冲突" -ForegroundColor Yellow
    
    # 检查iCloud同步状态
    try {
        $icloudProcess = Get-Process -Name "iCloudDrive" -ErrorAction SilentlyContinue
        if ($icloudProcess) {
            Write-Host "🔄 iCloud Drive进程正在运行" -ForegroundColor Yellow
            Write-Host "   进程ID: $($icloudProcess.Id)" -ForegroundColor Gray
        }
        else {
            Write-Host "⚠️  iCloud Drive进程未检测到" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ 无法检查iCloud Drive进程状态" -ForegroundColor Red
    }
}

Write-Host "`n=== 第二部分: 核心数据文件位置检查 ===" -ForegroundColor Cyan

# 定义预期的文件映射
$coreDataFiles = @{
    "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" = "02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv"
    "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx"
    "失效模式定义.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx"
}

Write-Host "检查核心数据文件..." -ForegroundColor White
foreach ($originalFile in $coreDataFiles.Keys) {
    $expectedLocation = $coreDataFiles[$originalFile]
    
    Write-Host "`n--- 检查文件: $originalFile ---" -ForegroundColor Yellow
    
    # 检查原始位置
    $originalExists = Test-FileLocation $originalFile "原始位置"
    $originalStatus = if ($originalExists) { Test-iCloudStatus $originalFile } else { "不存在" }
    
    # 检查目标位置
    $targetExists = Test-FileLocation $expectedLocation "目标位置"
    $targetStatus = if ($targetExists) { Test-iCloudStatus $expectedLocation } else { "不存在" }
    
    # 确定状态
    if ($originalExists -and $targetExists) {
        $status = "重复存在"
        $issue = "原始和目标位置都存在文件，可能导致冲突"
        Add-DiagnosticEntry "核心数据" $originalFile $expectedLocation "两处都存在" $status $issue
    }
    elseif ($targetExists -and -not $originalExists) {
        $status = "已移动"
        $issue = "无"
        Add-DiagnosticEntry "核心数据" $originalFile $expectedLocation $expectedLocation $status $issue
    }
    elseif ($originalExists -and -not $targetExists) {
        $status = "未移动"
        $issue = "文件仍在原始位置，移动操作失败"
        Add-DiagnosticEntry "核心数据" $originalFile $expectedLocation $originalFile $status $issue
    }
    else {
        $status = "丢失"
        $issue = "文件在两个位置都不存在，可能已丢失"
        Add-DiagnosticEntry "核心数据" $originalFile $expectedLocation "未找到" $status $issue
    }
    
    Write-Host "   原始位置状态: $originalStatus" -ForegroundColor Gray
    Write-Host "   目标位置状态: $targetStatus" -ForegroundColor Gray
    Write-Host "   诊断结果: $status" -ForegroundColor $(if ($status -eq "已移动") { "Green" } elseif ($status -eq "未移动") { "Yellow" } else { "Red" })
}

Write-Host "`n=== 第三部分: 分析脚本位置检查 ===" -ForegroundColor Cyan

$analysisScripts = @{
    "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py"
    "g3p_comprehensive_statistical_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_综合统计分析_v1.0.py"
    "g3p_resistance_welding_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_电阻焊接分析_v1.0.py"
    "G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py" = "02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py"
    "H1_Analysis_Manual.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py"
    "H1_Data_Verification.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py"
}

Write-Host "检查分析脚本..." -ForegroundColor White
foreach ($originalScript in $analysisScripts.Keys) {
    $expectedLocation = $analysisScripts[$originalScript]
    
    Write-Host "`n--- 检查脚本: $originalScript ---" -ForegroundColor Yellow
    
    $originalExists = Test-FileLocation $originalScript "原始位置"
    $targetExists = Test-FileLocation $expectedLocation "目标位置"
    
    if ($originalExists -and $targetExists) {
        $status = "重复存在"
        $issue = "脚本在两个位置都存在"
        Add-DiagnosticEntry "分析脚本" $originalScript $expectedLocation "两处都存在" $status $issue
    }
    elseif ($targetExists -and -not $originalExists) {
        $status = "已移动"
        $issue = "无"
        Add-DiagnosticEntry "分析脚本" $originalScript $expectedLocation $expectedLocation $status $issue
    }
    elseif ($originalExists -and -not $targetExists) {
        $status = "未移动"
        $issue = "脚本移动失败"
        Add-DiagnosticEntry "分析脚本" $originalScript $expectedLocation $originalScript $status $issue
    }
    else {
        $status = "丢失"
        $issue = "脚本文件丢失"
        Add-DiagnosticEntry "分析脚本" $originalScript $expectedLocation "未找到" $status $issue
    }
}

Write-Host "`n=== 第四部分: 可视化结果检查 ===" -ForegroundColor Cyan

$visualizationFiles = @{
    "g3p_data_exploration.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_数据探索_v1.0.png"
    "g3p_h1_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证_v1.0.png"
    "g3p_h2_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证_v1.0.png"
    "g3p_parameter_optimization.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化_v1.0.png"
    "g3p_prediction_models_v2.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_预测模型_v2.0.png"
}

Write-Host "检查可视化文件..." -ForegroundColor White
foreach ($originalViz in $visualizationFiles.Keys) {
    $expectedLocation = $visualizationFiles[$originalViz]
    
    $originalExists = Test-FileLocation $originalViz "原始位置"
    $targetExists = Test-FileLocation $expectedLocation "目标位置"
    
    if ($originalExists -and $targetExists) {
        $status = "重复存在"
        $issue = "可视化文件重复"
        Add-DiagnosticEntry "可视化" $originalViz $expectedLocation "两处都存在" $status $issue
    }
    elseif ($targetExists -and -not $originalExists) {
        $status = "已移动"
        $issue = "无"
        Add-DiagnosticEntry "可视化" $originalViz $expectedLocation $expectedLocation $status $issue
    }
    elseif ($originalExists -and -not $targetExists) {
        $status = "未移动"
        $issue = "可视化文件移动失败"
        Add-DiagnosticEntry "可视化" $originalViz $expectedLocation $originalViz $status $issue
    }
    else {
        $status = "丢失"
        $issue = "可视化文件丢失"
        Add-DiagnosticEntry "可视化" $originalViz $expectedLocation "未找到" $status $issue
    }
}

Write-Host "`n=== 第五部分: 文件夹结构验证 ===" -ForegroundColor Cyan

$requiredFolders = @(
    "01_Core_Technical_Documents/Final_Analysis",
    "01_Core_Technical_Documents/Project_Management", 
    "01_Core_Technical_Documents/Knowledge_Base",
    "02_Data_And_Analysis/Raw_Data",
    "02_Data_And_Analysis/Analysis_Scripts",
    "02_Data_And_Analysis/Reference_Scripts",
    "02_Data_And_Analysis/Results/Visualizations",
    "03_Official_Reports/Equipment_Documentation",
    "04_Reference_Documents/Historical_Analysis",
    "04_Reference_Documents/Process_Documentation",
    "04_Reference_Documents/Site_Photos",
    "05_Archive/Deprecated_Files/Early_Analysis",
    "05_Archive/Deprecated_Files/Duplicate_Scripts",
    "05_Archive/Deprecated_Files/Unrelated_Files"
)

Write-Host "检查文件夹结构..." -ForegroundColor White
$folderIssues = 0
foreach ($folder in $requiredFolders) {
    if (Test-Path $folder) {
        Write-Host "✅ 文件夹存在: $folder" -ForegroundColor Green
    }
    else {
        Write-Host "❌ 文件夹缺失: $folder" -ForegroundColor Red
        $folderIssues++
    }
}

Write-Host "`n=== 诊断总结 ===" -ForegroundColor Cyan

# 统计问题
$totalFiles = $diagnosticReport.Count
$movedFiles = ($diagnosticReport | Where-Object { $_.Status -eq "已移动" }).Count
$unmovedFiles = ($diagnosticReport | Where-Object { $_.Status -eq "未移动" }).Count
$duplicateFiles = ($diagnosticReport | Where-Object { $_.Status -eq "重复存在" }).Count
$lostFiles = ($diagnosticReport | Where-Object { $_.Status -eq "丢失" }).Count

Write-Host "文件移动统计:" -ForegroundColor Yellow
Write-Host "  总文件数: $totalFiles" -ForegroundColor White
Write-Host "  已成功移动: $movedFiles" -ForegroundColor Green
Write-Host "  未移动: $unmovedFiles" -ForegroundColor Yellow
Write-Host "  重复存在: $duplicateFiles" -ForegroundColor Red
Write-Host "  丢失文件: $lostFiles" -ForegroundColor Red
Write-Host "  文件夹问题: $folderIssues" -ForegroundColor Red

# 生成详细报告
$reportContent = @"
# G3P OP60项目文件重组诊断报告

**诊断时间**: $currentTime
**诊断状态**: $(if ($unmovedFiles -eq 0 -and $duplicateFiles -eq 0 -and $lostFiles -eq 0) { "✅ 正常" } else { "❌ 发现问题" })

## 📊 统计摘要

- **总检查文件数**: $totalFiles
- **成功移动**: $movedFiles
- **移动失败**: $unmovedFiles  
- **重复存在**: $duplicateFiles
- **文件丢失**: $lostFiles
- **文件夹问题**: $folderIssues

## 📋 详细检查结果

| 类别 | 文件名 | 预期位置 | 实际位置 | 状态 | 问题描述 |
|------|--------|----------|----------|------|----------|
"@

foreach ($entry in $diagnosticReport) {
    $reportContent += "`n| $($entry.Category) | $($entry.Item) | $($entry.ExpectedLocation) | $($entry.ActualLocation) | $($entry.Status) | $($entry.Issue) |"
}

$reportContent += @"

## 🔍 主要问题分析

### iCloud Drive干扰问题
- **检测结果**: $(if ($icloudPath -like "*iCloudDrive*") { "确认在iCloud Drive环境中" } else { "非iCloud Drive环境" })
- **影响**: iCloud同步机制可能干扰PowerShell文件操作
- **建议**: 使用robocopy或暂停iCloud同步

### 文件冲突问题  
- **重复文件数**: $duplicateFiles
- **影响**: 目标位置已存在同名文件，阻止移动操作
- **建议**: 使用强制覆盖或先清理目标位置

### 移动失败问题
- **失败文件数**: $unmovedFiles
- **可能原因**: 
  1. iCloud同步锁定文件
  2. 文件正在被其他程序使用
  3. 权限不足
  4. 目标文件夹不存在

## 💡 修复建议

### 立即行动
1. **暂停iCloud同步**: 在系统设置中暂时禁用iCloud Drive同步
2. **清理重复文件**: 删除目标位置的重复文件
3. **使用robocopy**: 替代PowerShell Move-Item命令
4. **分批处理**: 小批量移动文件以避免冲突

### 长期解决方案
1. **本地工作副本**: 在非iCloud位置创建工作副本
2. **同步策略**: 建立明确的iCloud同步策略
3. **自动化脚本**: 开发iCloud兼容的文件操作脚本

---
**诊断完成时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
"@

# 保存诊断报告
Set-Content -Path "DIAGNOSTIC_AUDIT_REPORT.md" -Value $reportContent -Encoding UTF8

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Green
Write-Host "详细诊断报告已保存到: DIAGNOSTIC_AUDIT_REPORT.md" -ForegroundColor Green
Write-Host "请查看报告了解具体问题和修复建议" -ForegroundColor Yellow

if ($unmovedFiles -gt 0 -or $duplicateFiles -gt 0 -or $lostFiles -gt 0) {
    Write-Host "`n⚠️  发现问题，建议运行修复脚本" -ForegroundColor Red
}
else {
    Write-Host "`n✅ 文件重组状态正常" -ForegroundColor Green
}
