#!/usr/bin/env python3
"""
G3P Disk Holder Resistance Welding DOE Analysis - Simple Version
Analysis without external dependencies
"""

import csv
import statistics
from collections import defaultdict, Counter

def load_csv_data(filename):
    """Load CSV data and return as list of dictionaries"""
    data = []
    with open(filename, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            # Convert numeric fields
            numeric_fields = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                            'disk_gap_a', 'disk_gap_b', 'weld_failure_type', 'room_temperature',
                            'gas_fill_pressure', 'gas_weight', 'electrode_pressure', 'electrode_height',
                            's1_squeeze', 's1_weld_heat', 's1_percent_current', 's1_hold',
                            's1_off', 's1_impulses', 's1_cool', 's2_squeeze', 's2_weld_heat',
                            's2_percent_current', 's2_hold', 's2_off', 's2_impulses', 's2_cool']
            
            for field in numeric_fields:
                if field in row and row[field] and row[field] != '-':
                    try:
                        row[field] = float(row[field])
                    except ValueError:
                        row[field] = None
                else:
                    row[field] = None
            
            # Only include double_s1s2 schedule type
            if row.get('schedule_type') == 'double_s1s2':
                data.append(row)
    
    return data

def analyze_failure_modes(data):
    """Analyze failure modes and frequencies"""
    print("\n" + "="*60)
    print("FAILURE MODE ANALYSIS")
    print("="*60)
    
    # Count failure types
    failure_counts = Counter()
    failure_details = {}
    leakage_counts = Counter()
    
    for row in data:
        failure_type = row.get('weld_failure_type')
        if failure_type is not None:
            failure_counts[int(failure_type)] += 1
            if int(failure_type) not in failure_details:
                failure_details[int(failure_type)] = row.get('failure_details', 'Unknown')
        
        leakage = row.get('leakage', 'Unknown')
        leakage_counts[leakage] += 1
    
    print("\nFailure Type Distribution:")
    total_samples = len(data)
    for failure_type in sorted(failure_counts.keys()):
        count = failure_counts[failure_type]
        detail = failure_details.get(failure_type, "Unknown")
        percentage = (count / total_samples) * 100
        print(f"Type {failure_type}: {count:3d} samples ({percentage:5.1f}%) - {detail}")
    
    print(f"\nLeakage Performance:")
    for status, count in leakage_counts.items():
        percentage = (count / total_samples) * 100
        print(f"{status}: {count:3d} samples ({percentage:5.1f}%)")
    
    # Calculate success rates
    success_count = failure_counts.get(1, 0)  # Type 1 = success
    pass_count = leakage_counts.get('Pass', 0)
    
    print(f"\nOverall Success Rate: {(success_count/total_samples)*100:.1f}%")
    print(f"Overall Leakage Pass Rate: {(pass_count/total_samples)*100:.1f}%")
    
    return failure_counts, leakage_counts

def analyze_parameter_ranges(data):
    """Analyze parameter ranges and distributions"""
    print("\n" + "="*60)
    print("PARAMETER RANGE ANALYSIS")
    print("="*60)
    
    # Key parameters to analyze
    key_params = ['s1_squeeze', 's1_weld_heat', 's1_percent_current', 's1_hold',
                  's2_squeeze', 's2_weld_heat', 's2_percent_current', 's2_hold']
    
    print("\nParameter Ranges and Distributions:")
    print("-" * 50)
    
    for param in key_params:
        values = [row[param] for row in data if row.get(param) is not None]
        if values:
            min_val = min(values)
            max_val = max(values)
            mean_val = statistics.mean(values)
            unique_vals = sorted(set(values))
            
            print(f"{param:20s}: {min_val:6.1f} - {max_val:6.1f} (mean: {mean_val:6.1f})")
            print(f"{'':20s}  Unique values: {unique_vals}")
        else:
            print(f"{param:20s}: No data")

def analyze_quality_metrics(data):
    """Analyze quality metrics"""
    print("\n" + "="*60)
    print("QUALITY METRICS ANALYSIS")
    print("="*60)
    
    # Quality metrics
    quality_metrics = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                      'disk_gap_a', 'disk_gap_b']
    
    print("\nQuality Metric Statistics:")
    print("-" * 50)
    
    for metric in quality_metrics:
        values = [row[metric] for row in data if row.get(metric) is not None]
        if values:
            min_val = min(values)
            max_val = max(values)
            mean_val = statistics.mean(values)
            std_val = statistics.stdev(values) if len(values) > 1 else 0
            
            print(f"{metric:30s}: {mean_val:6.2f} ± {std_val:5.2f} [{min_val:6.2f}, {max_val:6.2f}]")

def analyze_successful_combinations(data):
    """Analyze parameter combinations for successful welds"""
    print("\n" + "="*60)
    print("SUCCESSFUL PARAMETER COMBINATIONS")
    print("="*60)
    
    # Filter successful welds (failure_type = 1 and leakage = Pass)
    successful_welds = [row for row in data 
                       if row.get('weld_failure_type') == 1 and row.get('leakage') == 'Pass']
    
    print(f"\nSuccessful welds: {len(successful_welds)} out of {len(data)} total")
    
    if not successful_welds:
        print("No fully successful welds found!")
        return
    
    # Analyze parameter combinations in successful welds
    param_combinations = defaultdict(int)
    key_params = ['s1_percent_current', 's1_weld_heat', 's1_hold', 
                  's2_percent_current', 's2_weld_heat', 's2_hold']
    
    for row in successful_welds:
        combo = tuple(row.get(param) for param in key_params)
        if all(val is not None for val in combo):
            param_combinations[combo] += 1
    
    print(f"\nMost Common Successful Parameter Combinations:")
    print("Format: [s1_current, s1_heat, s1_hold, s2_current, s2_heat, s2_hold]")
    print("-" * 70)
    
    sorted_combos = sorted(param_combinations.items(), key=lambda x: x[1], reverse=True)
    for i, (combo, count) in enumerate(sorted_combos[:10]):
        print(f"{i+1:2d}. {list(combo)} (appears {count} times)")

def analyze_failure_patterns(data):
    """Analyze patterns in failed welds"""
    print("\n" + "="*60)
    print("FAILURE PATTERN ANALYSIS")
    print("="*60)
    
    # Group failures by type
    failure_groups = defaultdict(list)
    for row in data:
        failure_type = row.get('weld_failure_type')
        if failure_type is not None and failure_type != 1:  # Not success
            failure_groups[int(failure_type)].append(row)
    
    print(f"\nFailure Analysis by Type:")
    print("-" * 40)
    
    for failure_type in sorted(failure_groups.keys()):
        failures = failure_groups[failure_type]
        print(f"\nType {failure_type} Failures ({len(failures)} samples):")
        
        # Analyze height distribution for this failure type
        heights = [row['post_weld_disk_holder_height'] for row in failures 
                  if row.get('post_weld_disk_holder_height') is not None]
        
        if heights:
            mean_height = statistics.mean(heights)
            print(f"  Average height: {mean_height:.2f} mm")
            
            # Compare with successful welds
            successful_heights = [row['post_weld_disk_holder_height'] for row in data 
                                if row.get('weld_failure_type') == 1 and 
                                   row.get('post_weld_disk_holder_height') is not None]
            
            if successful_heights:
                success_mean = statistics.mean(successful_heights)
                print(f"  Success height avg: {success_mean:.2f} mm")
                print(f"  Difference: {mean_height - success_mean:+.2f} mm")

def main():
    """Main analysis function"""
    print("G3P Disk Holder Resistance Welding DOE Analysis")
    print("=" * 60)

    # Load data from both files
    all_data = []
    files_to_analyze = ["data_2025-01-18_03-46-49.csv", "2025-01-04_19-37-52.csv"]

    for filename in files_to_analyze:
        try:
            data = load_csv_data(filename)
            all_data.extend(data)
            print(f"Loaded {len(data)} double_s1s2 samples from {filename}")
        except FileNotFoundError:
            print(f"Warning: Could not find {filename}")

    if not all_data:
        print("Error: No data files found!")
        return

    print(f"Total samples for analysis: {len(all_data)}")
    data = all_data
    
    # Perform analyses
    analyze_failure_modes(data)
    analyze_parameter_ranges(data)
    analyze_quality_metrics(data)
    analyze_successful_combinations(data)
    analyze_failure_patterns(data)
    
    # Summary recommendations
    print("\n" + "="*60)
    print("SUMMARY AND RECOMMENDATIONS")
    print("="*60)
    
    total_samples = len(data)
    success_count = sum(1 for row in data if row.get('weld_failure_type') == 1)
    pass_count = sum(1 for row in data if row.get('leakage') == 'Pass')
    
    print(f"\nCurrent Performance Summary:")
    print(f"- Total Samples: {total_samples}")
    print(f"- Success Rate: {(success_count/total_samples)*100:.1f}%")
    print(f"- Leakage Pass Rate: {(pass_count/total_samples)*100:.1f}%")
    
    print(f"\nRecommended Next Steps:")
    print(f"1. Focus on reducing Type 4 (height) and Type 8 (crack) failures")
    print(f"2. Investigate parameter combinations from successful welds")
    print(f"3. Consider tightening process control on critical parameters")
    print(f"4. Design confirmation experiments for optimal settings")

if __name__ == "__main__":
    main()
