# G3P OP60项目文档索引

**索引创建日期**: 2025年8月3日  
**文档重组版本**: v1.0  
**总文档数**: 81个文件  
**重组后结构**: 5级文件夹，标准化命名

---

## 📋 核心技术文档索引

### 01_Core_Technical_Documents/ - 核心技术文档

#### Final_Analysis/ - 最终技术分析
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `01_FINAL_根因分析_v1.0_20250803.md` | 基于生产约束的最终根因分析 | 🟢 核心 | ⭐⭐⭐⭐⭐ |
| `02_EXEC_H1重新设计执行指令_v1.0_20250803.md` | 可执行的H1重新验证指令 | 🟢 核心 | ⭐⭐⭐⭐⭐ |

#### Project_Management/ - 项目管理
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `01_PROJ_文档管理计划_v1.0_20250803.md` | 完整的文档管理计划 | 🟢 核心 | ⭐⭐⭐⭐ |

#### Knowledge_Base/ - 知识库总结
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `01_KB_项目知识库总结_v1.0_20250803.md` | 项目核心知识和方法论总结 | 🟢 核心 | ⭐⭐⭐⭐⭐ |

---

## 📊 数据和分析索引

### 02_Data_And_Analysis/ - 数据和分析

#### Raw_Data/ - 原始数据
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `02_Data_And_Analysis/Raw_Data/02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv` | 核心实验数据 | 🟢 核心 | ⭐⭐⭐⭐⭐ |
| `02_Data_And_Analysis/Raw_Data/02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx` | 工艺参数定义 | 🟢 核心 | ⭐⭐⭐⭐ |

#### Analysis_Scripts/ - 核心分析脚本
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `02_Data_And_Analysis/Analysis_Scripts/02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py` | H1阶段统计分析脚本 | 🟢 核心 | ⭐⭐⭐⭐ |

#### Reference_Scripts/ - 参考脚本
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `02_Data_And_Analysis/Reference_Scripts/02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py` | 简化分析方法参考 | 🟡 参考 | ⭐⭐ |

#### Results/Visualizations/ - 数据可视化
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证_v1.0.png` | H1假设验证可视化 | 🟢 核心 | ⭐⭐⭐ |

---

## 📄 正式报告索引

### 03_Official_Reports/ - 正式报告

#### 主要报告
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `03_Official_Reports/03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf` | 阶段总结报告 | 🟢 核心 | ⭐⭐⭐⭐ |

#### Equipment_Documentation/ - 设备文档
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书_EN.pdf` | 设备技术文档 | 🟢 核心 | ⭐⭐⭐ |

---

## 📚 参考文档索引

### 04_Reference_Documents/ - 参考文档

#### Historical_Analysis/ - 历史分析
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `04_Reference_Documents/Historical_Analysis/REF_5M1E根因分析_v1.0_20250803.md` | 早期5M1E分析参考 | 🟡 参考 | ⭐⭐ |

#### Site_Photos/ - 现场照片
| 文件名 | 用途 | 状态 | 重要性 |
|--------|------|------|--------|
| `04_Reference_Documents/Site_Photos/PHOTO_生产线OP60现场照片集/` | 现场照片索引 | 🟡 参考 | ⭐⭐ |

---

## 🗄️ 归档文件索引

### 05_Archive/ - 归档文件

#### Deprecated_Files/ - 过时文件
- **Early_Analysis/** - 早期分析文档
- **Duplicate_Scripts/** - 重复脚本
- **Unrelated_Files/** - 无关文件

---

## 🔍 快速查找指南

### 按用途查找

#### 🎯 需要执行实验
→ `01_Core_Technical_Documents/Final_Analysis/02_EXEC_H1重新设计执行指令_v1.0_20250803.md`

#### 📊 需要了解根因分析
→ `01_Core_Technical_Documents/Final_Analysis/01_FINAL_根因分析_v1.0_20250803.md`

#### 💡 需要学习项目知识
→ `01_Core_Technical_Documents/Knowledge_Base/01_KB_项目知识库总结_v1.0_20250803.md`

#### 📈 需要分析数据
→ `02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv`
→ `02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py`

#### 📋 需要项目管理信息
→ `01_Core_Technical_Documents/Project_Management/01_PROJ_文档管理计划_v1.0_20250803.md`

### 按重要性查找

#### ⭐⭐⭐⭐⭐ 最高优先级
- 最终根因分析
- H1重新设计执行指令
- 项目知识库总结
- 核心实验数据

#### ⭐⭐⭐⭐ 高优先级
- 项目管理计划
- 工艺参数定义
- 核心分析脚本
- 阶段总结报告

#### ⭐⭐⭐ 中等优先级
- 设备技术文档
- 数据可视化图表

#### ⭐⭐ 参考价值
- 历史分析文档
- 参考脚本
- 现场照片

---

## 📝 文档状态说明

### 状态标识
- 🟢 **核心保留**: 包含最终正确结论，优先参考
- 🟡 **有条件保留**: 有参考价值但部分内容可能过时
- 🔴 **已归档**: 过时或错误内容，仅供历史参考

### 版本控制
- **v1.0**: 初始版本
- **v1.1**: 小幅修订
- **v2.0**: 重大更新

### 命名规范
- **FINAL_**: 最终版本文档
- **EXEC_**: 可执行指令
- **PROJ_**: 项目管理
- **KB_**: 知识库
- **DATA_**: 数据文件
- **SCRIPT_**: 脚本文件
- **RESULT_**: 分析结果
- **REPORT_**: 正式报告
- **REF_**: 参考文档

---

## 🔄 文档更新机制

### 更新频率
- **核心技术文档**: 根据项目进展更新
- **数据文件**: 实验完成后更新
- **分析脚本**: 方法改进时更新
- **索引文件**: 文档结构变化时更新

### 更新责任
- **技术负责人**: 核心技术文档
- **数据分析师**: 分析脚本和结果
- **项目经理**: 项目管理文档
- **文档管理员**: 索引和结构维护

---

**索引维护**: 定期更新，确保准确性  
**使用建议**: 优先使用🟢核心保留文档  
**问题反馈**: 发现问题请及时反馈更新


