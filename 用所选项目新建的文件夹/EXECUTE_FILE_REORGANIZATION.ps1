# G3P OP60 Project File Reorganization Script
# Date: 2025-08-03
# Purpose: Complete the physical file moves for the document reorganization

Write-Host "=== G3P OP60 Project File Reorganization Script ===" -ForegroundColor Green
Write-Host "Date: 2025-08-03" -ForegroundColor Yellow
Write-Host "Starting file reorganization..." -ForegroundColor Yellow

# Set error handling
$ErrorActionPreference = "Continue"

# Function to safely move files
function Safe-MoveFile {
    param(
        [string]$Source,
        [string]$Destination,
        [string]$Description
    )
    
    if (Test-Path $Source) {
        try {
            Move-Item -Path $Source -Destination $Destination -Force
            Write-Host "✅ Moved: $Description" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "❌ Failed to move: $Description - $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    else {
        Write-Host "⚠️  File not found: $Source" -ForegroundColor Yellow
        return $false
    }
}

# Function to safely rename files
function Safe-RenameFile {
    param(
        [string]$OldPath,
        [string]$NewName,
        [string]$Description
    )
    
    if (Test-Path $OldPath) {
        try {
            $directory = Split-Path $OldPath -Parent
            $newPath = Join-Path $directory $NewName
            Rename-Item -Path $OldPath -NewName $NewName -Force
            Write-Host "✅ Renamed: $Description" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "❌ Failed to rename: $Description - $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    else {
        Write-Host "⚠️  File not found: $OldPath" -ForegroundColor Yellow
        return $false
    }
}

Write-Host "`n=== STEP 1: Moving Core Data Files ===" -ForegroundColor Cyan

# Move core data files to Raw_Data
Safe-MoveFile "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" "02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv" "Core experimental data"
Safe-MoveFile "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx" "Parameter definition table"
Safe-MoveFile "失效模式定义.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx" "Failure mode definitions"

Write-Host "`n=== STEP 2: Moving Analysis Scripts ===" -ForegroundColor Cyan

# Move core analysis scripts
Safe-MoveFile "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py" "Core Stage 1 analysis script"
Safe-MoveFile "g3p_comprehensive_statistical_analysis.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_综合统计分析_v1.0.py" "Comprehensive statistical analysis"
Safe-MoveFile "g3p_resistance_welding_analysis.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_电阻焊接分析_v1.0.py" "Resistance welding analysis"

# Move reference scripts
Safe-MoveFile "G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py" "02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py" "Simple analysis reference"
Safe-MoveFile "H1_Analysis_Manual.py" "02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py" "H1 manual analysis reference"
Safe-MoveFile "H1_Data_Verification.py" "02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py" "H1 data verification reference"

Write-Host "`n=== STEP 3: Moving Visualization Results ===" -ForegroundColor Cyan

# Move visualization files
Safe-MoveFile "g3p_data_exploration.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_数据探索_v1.0.png" "Data exploration visualization"
Safe-MoveFile "g3p_h1_hypothesis_verification.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证_v1.0.png" "H1 hypothesis verification"
Safe-MoveFile "g3p_h2_hypothesis_verification.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证_v1.0.png" "H2 hypothesis verification"
Safe-MoveFile "g3p_parameter_optimization.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化_v1.0.png" "Parameter optimization"
Safe-MoveFile "g3p_prediction_models_v2.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_预测模型_v2.0.png" "Prediction models v2"

Write-Host "`n=== STEP 4: Moving Official Reports ===" -ForegroundColor Cyan

# Move official reports
Safe-MoveFile "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf" "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf" "Phase summary report"
Safe-MoveFile "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pptx" "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pptx" "Phase summary presentation"
Safe-MoveFile "Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf" "03_Official_Reports/REPORT_优化参数_G3P_OP60_20250731.pdf" "Optimized parameters report"

# Move equipment documentation
Safe-MoveFile "充气机电阻焊接说明书 - 英文版.pdf" "03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书_EN.pdf" "Equipment manual (English)"
Safe-MoveFile "B0742300.pdf" "03_Official_Reports/Equipment_Documentation/EQUIP_B0742300.pdf" "Equipment document B0742300"
Safe-MoveFile "B0984900.pdf" "03_Official_Reports/Equipment_Documentation/EQUIP_B0984900.pdf" "Equipment document B0984900"

Write-Host "`n=== STEP 5: Moving Reference Documents ===" -ForegroundColor Cyan

# Move process documentation
Safe-MoveFile "Resistance Welding Process.pdf" "04_Reference_Documents/Process_Documentation/PROC_电阻焊接工艺_v1.0.pdf" "Resistance welding process"
Safe-MoveFile "Resistance Welding Process.docx" "04_Reference_Documents/Process_Documentation/PROC_电阻焊接工艺_v1.0.docx" "Resistance welding process (Word)"
Safe-MoveFile "G3P DOE文档解读.docx" "04_Reference_Documents/Process_Documentation/PROC_DOE文档解读_v1.0.docx" "DOE document interpretation"

# Move site photos folder
if (Test-Path "生产线OP60图片") {
    Safe-MoveFile "生产线OP60图片" "04_Reference_Documents/Site_Photos/PHOTO_生产线OP60现场照片集" "Site photos collection"
}

Write-Host "`n=== STEP 6: Moving Historical Analysis Documents ===" -ForegroundColor Cyan

# Move historical analysis documents with deprecation warnings
$historicalDocs = @(
    "DOE_v7.1_H1阶段5M1E根因分析报告_20250802.md",
    "DOE_v7.1_反向验证和根因分析执行总结_20250802.md", 
    "DOE_v7.1_第一阶段H1假设验证统计分析报告.md",
    "G3P_OP60_DOE执行指令_V7.1_Word版本.md",
    "DOE_v7.1_H1验证统计分析审核报告_20250802.md",
    "DOE_v7.1_设计与实际结果偏差分析报告_20250802.md",
    "DOE_v7.1_阶段性总结分析_执行报告_20250802.md"
)

foreach ($doc in $historicalDocs) {
    $newName = "REF_" + $doc.Replace("DOE_v7.1_", "").Replace("_20250802", "_v1.0_20250803")
    Safe-MoveFile $doc "04_Reference_Documents/Historical_Analysis/$newName" "Historical analysis: $doc"
}

Write-Host "`n=== STEP 7: Moving Archive Files ===" -ForegroundColor Cyan

# Move deprecated files to archive
$deprecatedFiles = @(
    "DOE_v7_1_Stage1_重新分析报告.md",
    "DOE_v8_0_验证性实验设计文档.md",
    "G3P_OP60_V7.1_DOE方案深度解读.md",
    "G3P_OP60_专业技术报告.html",
    "G3P_OP60_专业技术报告.md",
    "G3P_OP60_修正报告方法论完整性评估.md"
)

foreach ($file in $deprecatedFiles) {
    Safe-MoveFile $file "05_Archive/Deprecated_Files/Early_Analysis/$file" "Deprecated: $file"
}

# Move duplicate scripts
$duplicateScripts = @(
    "g3p_analysis_simple.py",
    "g3p_final_analysis.py", 
    "g3p_failure_analysis.py",
    "analysis_summary.py",
    "current_status_analysis.py"
)

foreach ($script in $duplicateScripts) {
    Safe-MoveFile $script "05_Archive/Deprecated_Files/Duplicate_Scripts/$script" "Duplicate script: $script"
}

Write-Host "`n=== STEP 8: Cleaning Up Analysis Folder ===" -ForegroundColor Cyan

# Move Analysis folder contents
if (Test-Path "Analysis") {
    Safe-MoveFile "Analysis/image.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_分析图像_v1.0.png" "Analysis image"
    Safe-MoveFile "Analysis/微信图片_20241130191620.png" "02_Data_And_Analysis/Results/Visualizations/RESULT_微信图片_20241130.png" "WeChat image"
    
    # Remove empty Analysis folder
    try {
        Remove-Item "Analysis" -Recurse -Force
        Write-Host "✅ Removed empty Analysis folder" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️  Could not remove Analysis folder: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host "`n=== STEP 9: Moving G3P_OP60_第一阶段数据分析 Folder ===" -ForegroundColor Cyan

# Move the entire analysis folder
if (Test-Path "G3P_OP60_第一阶段数据分析") {
    Safe-MoveFile "G3P_OP60_第一阶段数据分析" "05_Archive/Deprecated_Files/Early_Analysis/G3P_OP60_第一阶段数据分析" "Early analysis folder"
}

Write-Host "`n=== REORGANIZATION COMPLETE ===" -ForegroundColor Green
Write-Host "File reorganization script execution completed!" -ForegroundColor Green
Write-Host "Please check the results above and run the documentation update script next." -ForegroundColor Yellow

# Generate completion report
$completionTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
Write-Host "`nReorganization completed at: $completionTime" -ForegroundColor Cyan
Write-Host "Next step: Run the documentation update script to reflect the new file locations." -ForegroundColor Yellow
