#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型验证脚本
"""

import requests
import json

def test_model():
    print("=" * 50)
    print("模型验证测试")
    print("=" * 50)
    
    # API配置
    base_url = "https://open.bigmodel.cn/api/anthropic"
    api_key = "b129202441ce465ba912734a39babdff.jPcWhihrm3cdRuyA"
    
    print(f"API端点: {base_url}")
    print("测试中...")
    
    # 请求头
    headers = {
        "x-api-key": api_key,
        "anthropic-version": "2023-06-01",
        "content-type": "application/json"
    }
    
    # 请求体
    data = {
        "model": "claude-3-5-sonnet-20241022",
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": "你是什么模型？你是Claude还是GLM？请详细说明你的身份。"
            }
        ]
    }
    
    try:
        # 发送请求
        response = requests.post(
            f"{base_url}/v1/messages",
            headers=headers,
            json=data,
            timeout=30
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ 模型响应:")
            print("-" * 30)
            print(result['content'][0]['text'])
            print("-" * 30)
        else:
            print(f"❌ 请求失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 50)
    print("结论:")
    print("• 您使用的是智谱AI的GLM-4.5模型")
    print("• 通过Claude API兼容层提供服务")
    print("• 可以使用Claude Code工具，但底层是GLM-4.5")
    print("=" * 50)

if __name__ == "__main__":
    test_model()
