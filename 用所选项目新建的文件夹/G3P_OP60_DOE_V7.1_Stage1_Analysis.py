# G3P OP60 DOE v7.1 第一阶段(H1假设验证)统计分析
# 分析日期: 2025年8月2日
# 数据源: NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv

import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import chi2_contingency, fisher_exact
import warnings
warnings.filterwarnings('ignore')

def load_and_filter_stage1_data():
    """加载并筛选第一阶段H1验证数据"""
    print("=== 加载DOE v7.1第一阶段数据 ===")
    
    # 读取数据
    df = pd.read_csv('NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv')
    
    # 筛选H1阶段数据 (H1-01 到 H1-30)
    stage1_mask = df['group#'].str.contains('H1-', na=False)
    stage1_data = df[stage1_mask].copy()
    
    print(f"总数据行数: {len(df)}")
    print(f"H1阶段数据行数: {len(stage1_data)}")
    
    # 验证数据完整性
    if len(stage1_data) != 30:
        print(f"⚠️ 警告: 期望30个H1试验，实际找到{len(stage1_data)}个")
    
    # 显示H1数据概览
    print("\n=== H1阶段数据概览 ===")
    print(stage1_data[['group#', 'sample_id', 's1_percent_current', 'weld_failure_type', 
                      'post_weld_disk_holder_height', 'gas_fill_pressure']].head(10))
    
    return stage1_data

def analyze_h1_hypothesis(data):
    """H1假设验证分析: 97% vs 99% S1电流对Type 8裂纹失效的影响"""
    print("\n" + "="*60)
    print("H1假设验证分析: S1电流对Type 8裂纹失效的影响")
    print("="*60)
    
    # 分组数据
    group_97 = data[data['s1_percent_current'] == 97].copy()
    group_99 = data[data['s1_percent_current'] == 99].copy()
    
    print(f"\n97%电流组样本数: {len(group_97)}")
    print(f"99%电流组样本数: {len(group_99)}")
    
    # 分析失效类型分布
    print("\n=== 失效类型分布分析 ===")
    
    # 97%组失效分析
    failure_97 = group_97['weld_failure_type'].value_counts()
    success_rate_97 = (failure_97.get(1, 0) / len(group_97)) * 100
    type4_rate_97 = (failure_97.get(4, 0) / len(group_97)) * 100
    type8_rate_97 = (failure_97.get(8, 0) / len(group_97)) * 100
    
    print(f"\n97%电流组 (n={len(group_97)}):")
    print(f"  Type 1 (成功): {failure_97.get(1, 0)}件 ({success_rate_97:.1f}%)")
    print(f"  Type 4 (高度失效): {failure_97.get(4, 0)}件 ({type4_rate_97:.1f}%)")
    print(f"  Type 8 (裂纹失效): {failure_97.get(8, 0)}件 ({type8_rate_97:.1f}%)")
    
    # 99%组失效分析
    failure_99 = group_99['weld_failure_type'].value_counts()
    success_rate_99 = (failure_99.get(1, 0) / len(group_99)) * 100
    type4_rate_99 = (failure_99.get(4, 0) / len(group_99)) * 100
    type8_rate_99 = (failure_99.get(8, 0) / len(group_99)) * 100
    
    print(f"\n99%电流组 (n={len(group_99)}):")
    print(f"  Type 1 (成功): {failure_99.get(1, 0)}件 ({success_rate_99:.1f}%)")
    print(f"  Type 4 (高度失效): {failure_99.get(4, 0)}件 ({type4_rate_99:.1f}%)")
    print(f"  Type 8 (裂纹失效): {failure_99.get(8, 0)}件 ({type8_rate_99:.1f}%)")
    
    # H1假设统计检验
    print("\n=== H1假设统计检验 ===")
    
    # 创建列联表 (Type 8 vs 非Type 8)
    type8_97 = failure_97.get(8, 0)
    non_type8_97 = len(group_97) - type8_97
    type8_99 = failure_99.get(8, 0)
    non_type8_99 = len(group_99) - type8_99
    
    contingency_table = np.array([[type8_97, non_type8_97],
                                  [type8_99, non_type8_99]])
    
    print(f"\n列联表 (Type 8裂纹失效):")
    print(f"                Type 8    非Type 8    总计")
    print(f"97%电流组        {type8_97:2d}        {non_type8_97:2d}       {len(group_97)}")
    print(f"99%电流组        {type8_99:2d}        {non_type8_99:2d}       {len(group_99)}")
    
    # Fisher精确检验 (适用于小样本)
    if type8_97 + type8_99 > 0:
        odds_ratio, p_value_fisher = fisher_exact(contingency_table)
        print(f"\nFisher精确检验结果:")
        print(f"  优势比 (Odds Ratio): {odds_ratio:.3f}")
        print(f"  p值: {p_value_fisher:.4f}")
        print(f"  统计显著性: {'✅ 显著 (p<0.05)' if p_value_fisher < 0.05 else '❌ 不显著 (p≥0.05)'}")
    else:
        print(f"\n⚠️ 注意: 两组均无Type 8失效，无法进行统计检验")
        p_value_fisher = 1.0
        odds_ratio = 1.0
    
    return {
        'group_97_n': len(group_97),
        'group_99_n': len(group_99),
        'success_rate_97': success_rate_97,
        'success_rate_99': success_rate_99,
        'type4_rate_97': type4_rate_97,
        'type4_rate_99': type4_rate_99,
        'type8_rate_97': type8_rate_97,
        'type8_rate_99': type8_rate_99,
        'p_value_fisher': p_value_fisher,
        'odds_ratio': odds_ratio,
        'group_97': group_97,
        'group_99': group_99
    }

def analyze_weld_height(data, h1_results):
    """焊接高度分析"""
    print("\n" + "="*60)
    print("焊后高度分析")
    print("="*60)
    
    group_97 = h1_results['group_97']
    group_99 = h1_results['group_99']
    
    # 高度数据提取
    heights_97 = group_97['post_weld_disk_holder_height'].dropna()
    heights_99 = group_99['post_weld_disk_holder_height'].dropna()
    
    print(f"\n=== 焊接高度描述性统计 ===")
    print(f"97%电流组 (n={len(heights_97)}):")
    print(f"  平均值: {heights_97.mean():.3f} mm")
    print(f"  标准差: {heights_97.std():.3f} mm")
    print(f"  范围: {heights_97.min():.3f} - {heights_97.max():.3f} mm")
    print(f"  目标范围符合率: {((heights_97 >= 2.1) & (heights_97 <= 2.4)).sum()}/{len(heights_97)} ({((heights_97 >= 2.1) & (heights_97 <= 2.4)).mean()*100:.1f}%)")
    
    print(f"\n99%电流组 (n={len(heights_99)}):")
    print(f"  平均值: {heights_99.mean():.3f} mm")
    print(f"  标准差: {heights_99.std():.3f} mm")
    print(f"  范围: {heights_99.min():.3f} - {heights_99.max():.3f} mm")
    print(f"  目标范围符合率: {((heights_99 >= 2.1) & (heights_99 <= 2.4)).sum()}/{len(heights_99)} ({((heights_99 >= 2.1) & (heights_99 <= 2.4)).mean()*100:.1f}%)")
    
    # 两组高度差异检验
    if len(heights_97) > 0 and len(heights_99) > 0:
        t_stat, p_value_height = stats.ttest_ind(heights_97, heights_99)
        effect_size = (heights_99.mean() - heights_97.mean()) / np.sqrt(((len(heights_97)-1)*heights_97.var() + (len(heights_99)-1)*heights_99.var()) / (len(heights_97)+len(heights_99)-2))
        
        print(f"\n=== 两组高度差异检验 ===")
        print(f"t检验统计量: {t_stat:.3f}")
        print(f"p值: {p_value_height:.4f}")
        print(f"效应大小 (Cohen's d): {effect_size:.3f}")
        print(f"统计显著性: {'✅ 显著差异 (p<0.05)' if p_value_height < 0.05 else '❌ 无显著差异 (p≥0.05)'}")
        print(f"平均高度差异: {heights_99.mean() - heights_97.mean():.3f} mm")
    
    # Type 4高度失效分析
    print(f"\n=== Type 4高度失效分析 ===")
    type4_97 = (group_97['weld_failure_type'] == 4).sum()
    type4_99 = (group_99['weld_failure_type'] == 4).sum()
    
    print(f"97%电流组Type 4失效: {type4_97}/{len(group_97)} ({type4_97/len(group_97)*100:.1f}%)")
    print(f"99%电流组Type 4失效: {type4_99}/{len(group_99)} ({type4_99/len(group_99)*100:.1f}%)")
    
    return {
        'heights_97_mean': heights_97.mean(),
        'heights_97_std': heights_97.std(),
        'heights_99_mean': heights_99.mean(),
        'heights_99_std': heights_99.std(),
        'height_p_value': p_value_height if 'p_value_height' in locals() else None,
        'height_effect_size': effect_size if 'effect_size' in locals() else None
    }

def analyze_process_parameters(data):
    """工艺参数分析"""
    print("\n" + "="*60)
    print("工艺参数分析")
    print("="*60)
    
    # 关键参数分析
    key_params = ['gas_fill_pressure', 'electrode_pressure', 'room_temperature', 's2_percent_current', 's2_hold']
    
    for param in key_params:
        if param in data.columns:
            param_data = data[param].dropna()
            if len(param_data) > 0:
                print(f"\n{param}:")
                print(f"  平均值: {param_data.mean():.2f}")
                print(f"  标准差: {param_data.std():.2f}")
                print(f"  范围: {param_data.min():.2f} - {param_data.max():.2f}")

def generate_stage1_report(h1_results, height_results):
    """生成第一阶段分析报告"""
    print("\n" + "="*80)
    print("DOE v7.1 第一阶段(H1假设验证)统计分析报告")
    print("="*80)
    
    print(f"\n📊 **实验设计执行情况**")
    print(f"- 计划样本数: 30 (每组15个)")
    print(f"- 实际样本数: {h1_results['group_97_n'] + h1_results['group_99_n']}")
    print(f"- 97%电流组: {h1_results['group_97_n']}个样本")
    print(f"- 99%电流组: {h1_results['group_99_n']}个样本")
    print(f"- 数据完整性: {'✅ 符合设计要求' if h1_results['group_97_n'] == 15 and h1_results['group_99_n'] == 15 else '⚠️ 样本数不符合设计'}")
    
    print(f"\n🎯 **H1假设验证结果**")
    print(f"假设内容: 99%S1电流相比97%电流会显著增加Type 8裂纹失效率")
    print(f"- 97%电流组Type 8失效率: {h1_results['type8_rate_97']:.1f}%")
    print(f"- 99%电流组Type 8失效率: {h1_results['type8_rate_99']:.1f}%")
    print(f"- 统计检验p值: {h1_results['p_value_fisher']:.4f}")
    print(f"- 优势比: {h1_results['odds_ratio']:.3f}")
    
    if h1_results['p_value_fisher'] < 0.05:
        conclusion = "✅ H1假设得到统计支持"
        recommendation = "建议在第二阶段采用97%S1电流以降低裂纹风险"
    else:
        conclusion = "❌ H1假设未得到统计支持"
        recommendation = "两种电流水平的裂纹风险无显著差异，可根据其他因素选择"
    
    print(f"- **结论**: {conclusion}")
    print(f"- **建议**: {recommendation}")
    
    print(f"\n📏 **焊接高度分析结果**")
    print(f"- 97%电流组平均高度: {height_results['heights_97_mean']:.3f}±{height_results['heights_97_std']:.3f} mm")
    print(f"- 99%电流组平均高度: {height_results['heights_99_mean']:.3f}±{height_results['heights_99_std']:.3f} mm")
    if height_results['height_p_value'] is not None:
        print(f"- 高度差异显著性: {'✅ 显著差异' if height_results['height_p_value'] < 0.05 else '❌ 无显著差异'} (p={height_results['height_p_value']:.4f})")
    
    print(f"\n📈 **整体质量表现**")
    print(f"- 97%电流组成功率: {h1_results['success_rate_97']:.1f}%")
    print(f"- 99%电流组成功率: {h1_results['success_rate_99']:.1f}%")
    print(f"- 97%电流组Type 4失效率: {h1_results['type4_rate_97']:.1f}%")
    print(f"- 99%电流组Type 4失效率: {h1_results['type4_rate_99']:.1f}%")
    
    print(f"\n🚀 **第二阶段执行建议**")
    
    # 基于结果决定是否继续第二阶段
    total_success_rate = (h1_results['success_rate_97'] + h1_results['success_rate_99']) / 2
    
    if total_success_rate > 50:
        stage2_recommendation = "✅ 建议继续执行第二阶段CCD建模"
        print(f"- **执行决策**: {stage2_recommendation}")
        print(f"- **理由**: 整体成功率{total_success_rate:.1f}%，具备进一步优化价值")
        
        if h1_results['type8_rate_97'] < h1_results['type8_rate_99']:
            print(f"- **参数建议**: 第二阶段建议采用97%S1电流作为基准")
        else:
            print(f"- **参数建议**: 两种电流水平可根据其他优化目标选择")
            
    else:
        stage2_recommendation = "⚠️ 建议暂停第二阶段，优先解决基础工艺问题"
        print(f"- **执行决策**: {stage2_recommendation}")
        print(f"- **理由**: 整体成功率{total_success_rate:.1f}%过低，需要基础工艺改进")
    
    return {
        'stage2_recommendation': stage2_recommendation,
        'total_success_rate': total_success_rate
    }

def main():
    """主分析函数"""
    print("G3P OP60 DOE v7.1 第一阶段(H1假设验证)统计分析")
    print("分析时间:", pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'))
    print("="*80)
    
    # 1. 加载数据
    stage1_data = load_and_filter_stage1_data()
    
    # 2. H1假设验证分析
    h1_results = analyze_h1_hypothesis(stage1_data)
    
    # 3. 焊接高度分析
    height_results = analyze_weld_height(stage1_data, h1_results)
    
    # 4. 工艺参数分析
    analyze_process_parameters(stage1_data)
    
    # 5. 生成综合报告
    final_results = generate_stage1_report(h1_results, height_results)

    print(f"\n" + "="*80)
    print("分析完成！")
    print("="*80)

    return stage1_data, h1_results, height_results, final_results

if __name__ == "__main__":
    stage1_data, h1_results, height_results, final_results = main()
