#!/usr/bin/env python3
"""
PDF文本提取工具
尝试使用多种方法提取PDF内容
"""

import sys
import os
import subprocess

def extract_with_pdfplumber(pdf_path):
    """使用pdfplumber提取PDF文本"""
    try:
        import pdfplumber
        with pdfplumber.open(pdf_path) as pdf:
            text = ""
            for page in pdf.pages:
                text += page.extract_text() + "\n"
        return text
    except ImportError:
        return None
    except Exception as e:
        print(f"pdfplumber error: {e}")
        return None

def extract_with_pypdf2(pdf_path):
    """使用PyPDF2提取PDF文本"""
    try:
        import PyPDF2
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
        return text
    except ImportError:
        return None
    except Exception as e:
        print(f"PyPDF2 error: {e}")
        return None

def extract_with_system_tools(pdf_path):
    """使用系统工具提取PDF文本"""
    try:
        # 尝试使用pdftotext
        result = subprocess.run(['pdftotext', pdf_path, '-'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout
    except FileNotFoundError:
        pass
    
    try:
        # 尝试使用mdls (macOS)
        result = subprocess.run(['mdls', '-name', 'kMDItemTextContent', pdf_path], 
                              capture_output=True, text=True)
        if result.returncode == 0 and 'kMDItemTextContent' in result.stdout:
            return result.stdout
    except FileNotFoundError:
        pass
    
    return None

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 pdf_extractor.py <pdf_file>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        print(f"File not found: {pdf_path}")
        sys.exit(1)
    
    print(f"Extracting text from: {pdf_path}")
    print("=" * 50)
    
    # 尝试多种方法
    methods = [
        ("pdfplumber", extract_with_pdfplumber),
        ("PyPDF2", extract_with_pypdf2),
        ("system tools", extract_with_system_tools)
    ]
    
    for method_name, method_func in methods:
        print(f"Trying {method_name}...")
        text = method_func(pdf_path)
        if text and text.strip():
            print(f"Success with {method_name}!")
            print("=" * 50)
            print(text[:2000])  # 显示前2000字符
            if len(text) > 2000:
                print("\n... (truncated)")
            return
        else:
            print(f"Failed with {method_name}")
    
    print("All extraction methods failed.")

if __name__ == "__main__":
    main()
