#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析G3P OP60数据，查看是否有H1验证数据
"""

import pandas as pd
import numpy as np

# 读取数据
file_path = r'C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv'
df = pd.read_csv(file_path, skip_blank_lines=True)

# 过滤掉空行和无效数据
df = df[df['no.'].notna() & (df['no.'] != '#N/A')]

# 检查数据结构
print('数据列名:', df.columns.tolist())
print('前10个样品ID:', df['sample_id'].head(10).tolist())
print('数据总行数:', len(df))

# 检查是否有任何H1开头的样品
h1_samples = df[df['sample_id'].str.startswith('H1-', na=False)]
print('H1开头的样品数:', len(h1_samples))

# 检查所有唯一的样品ID前缀
unique_ids = df['sample_id'].dropna().unique()
prefixes = [str(id)[:2] for id in unique_ids if pd.notna(id)]
print('所有样品ID前缀:', sorted(set(prefixes)))

# 检查S1电流的分布
df['s1_current_pct'] = pd.to_numeric(df['s1_percent_current'], errors='coerce')
s1_currents = df['s1_current_pct'].dropna().unique()
print('所有S1电流值:', sorted(s1_currents))
print('97%电流样品数:', len(df[df['s1_current_pct'] == 97]))
print('99%电流样品数:', len(df[df['s1_current_pct'] == 99]))

# 查看样品ID的模式
print('\n所有样品ID:')
for i, sample_id in enumerate(df['sample_id'].head(20)):
    print(f'{i+1:2d}. {sample_id}')

# 检查是否有验证数据的特点
print('\n检查数据特点:')
print('S1电流为97或99的样品数:', len(df[df['s1_current_pct'].isin([97, 99])]))
print('有泄漏测试结果的样品数:', len(df[df['leakage'].notna()]))
print('有失效类型记录的样品数:', len(df[df['weld_failure_type'].notna()]))

# 查看是否有可能是验证数据的样品
verification_candidates = df[df['s1_current_pct'].isin([97, 99])]
print(f'\n可能的验证样品数: {len(verification_candidates)}')
print('这些样品的ID:')
for i, sample_id in enumerate(verification_candidates['sample_id'].head(10)):
    print(f'{i+1:2d}. {sample_id}')