# G3P OP60 文件移动操作指令

**执行日期**: 2025年8月2日  
**操作方式**: 手动文件移动  
**预计时间**: 30-45分钟

---

## 🎯 核心数据文件移动

### 移动到 `02_Data_And_Analysis/Raw_Data/`

#### 实验数据文件
```
源文件: NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv
目标: 02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv
操作: 移动并重命名
```

#### 参数定义文件
```
源文件: G3P_OP60_破裂盘电阻焊接参数定义表.xlsx
目标: 02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx
操作: 移动并重命名
```

#### 失效模式定义
```
源文件: 失效模式定义.xlsx
目标: 02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx
操作: 移动并重命名
```

---

## 📊 分析脚本移动

### 移动到 `02_Data_And_Analysis/Analysis_Scripts/`

#### 核心分析脚本
```
源文件: G3P_OP60_DOE_V7.1_Stage1_Analysis.py
目标: 02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py
操作: 移动并重命名
```

### 移动到 `02_Data_And_Analysis/Reference_Scripts/`

#### 参考脚本 (选择重要的几个)
```
源文件: G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py
目标: 02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py
操作: 移动并重命名

源文件: H1_Analysis_Manual.py
目标: 02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py
操作: 移动并重命名

源文件: H1_Data_Verification.py
目标: 02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py
操作: 移动并重命名
```

---

## 📈 可视化结果移动

### 移动到 `02_Data_And_Analysis/Results/Visualizations/`

#### 图表文件
```
源文件: g3p_h1_hypothesis_verification.png
目标: 02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证图表.png
操作: 移动并重命名

源文件: g3p_h2_hypothesis_verification.png
目标: 02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证图表.png
操作: 移动并重命名

源文件: g3p_parameter_optimization.png
目标: 02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化图表.png
操作: 移动并重命名
```

---

## 📄 正式报告移动

### 移动到 `03_Official_Reports/`

#### 主要报告
```
源文件: NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf
目标: 03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf
操作: 移动并重命名

源文件: Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf
目标: 03_Official_Reports/REPORT_优化参数_G3P_OP60_20250731.pdf
操作: 移动并重命名
```

### 移动到 `03_Official_Reports/Equipment_Documentation/`

#### 设备文档
```
源文件: 充气机电阻焊接说明书 - 英文版.pdf
目标: 03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书.pdf
操作: 移动并重命名

源文件: B0742300.pdf
目标: 03_Official_Reports/Equipment_Documentation/EQUIP_B0742300.pdf
操作: 移动并重命名

源文件: B0984900.pdf
目标: 03_Official_Reports/Equipment_Documentation/EQUIP_B0984900.pdf
操作: 移动并重命名
```

---

## 📚 参考文档移动

### 移动到 `04_Reference_Documents/Site_Photos/`

#### 现场照片
```
源文件夹: 生产线OP60图片/
目标: 04_Reference_Documents/Site_Photos/生产线OP60现场照片/
操作: 移动整个文件夹
```

### 移动到 `04_Reference_Documents/Process_Documentation/`

#### 工艺文档
```
源文件: Resistance Welding Process.pdf
目标: 04_Reference_Documents/Process_Documentation/PROC_电阻焊接工艺.pdf
操作: 移动并重命名

源文件: double_s1s2 优化阶段参数矩阵表.docx
目标: 04_Reference_Documents/Process_Documentation/PROC_参数矩阵表.docx
操作: 移动并重命名
```

---

## 🗄️ 归档文件移动

### 移动到 `05_Archive/Deprecated_Files/Early_Analysis/`

#### 早期分析文档 (选择主要的)
```
源文件: DOE_v7.1_H1阶段5M1E根因分析报告_20250802.md
目标: 05_Archive/Deprecated_Files/Early_Analysis/
操作: 移动 (已有过时标注)

源文件: DOE_v7.1_反向验证和根因分析执行总结_20250802.md
目标: 05_Archive/Deprecated_Files/Early_Analysis/
操作: 移动 (已有过时标注)
```

### 移动到 `05_Archive/Deprecated_Files/Duplicate_Scripts/`

#### 重复脚本
```
源文件夹: G3P_OP60_第一阶段数据分析/
目标: 05_Archive/Deprecated_Files/Duplicate_Scripts/
操作: 移动整个文件夹

源文件夹: __pycache__/
目标: 05_Archive/Deprecated_Files/Duplicate_Scripts/
操作: 移动整个文件夹
```

---

## ⚠️ 操作注意事项

### 移动前检查
1. **确认目标文件夹存在**: 检查所有目标文件夹已创建
2. **备份重要文件**: 对关键文件进行备份
3. **检查文件占用**: 确保文件未被其他程序占用

### 重命名规范
- **DATA_**: 数据文件前缀
- **SCRIPT_**: 脚本文件前缀  
- **RESULT_**: 结果文件前缀
- **REPORT_**: 报告文件前缀
- **EQUIP_**: 设备文档前缀
- **PROC_**: 工艺文档前缀
- **REF_**: 参考文件前缀

### 版本标识
- 添加版本号: `_v1.0`
- 添加日期: `_20250802` (如适用)

---

## 📋 操作检查清单

### 完成后验证
- [ ] 核心数据文件已正确移动
- [ ] 分析脚本已分类移动
- [ ] 可视化结果已归档
- [ ] 正式报告已整理
- [ ] 参考文档已分类
- [ ] 过时文件已归档
- [ ] 文件命名符合规范
- [ ] 目录结构完整

### 清理工作
- [ ] 删除空文件夹
- [ ] 清理根目录
- [ ] 更新索引文件
- [ ] 验证文件完整性

---

**预计完成时间**: 30-45分钟  
**建议操作时间**: 一次性完成，避免中断  
**完成后**: 运行质量验证检查
