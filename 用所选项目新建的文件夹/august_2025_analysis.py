#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 8月份2025年测试结果分析
分析8月份的测试数据并审核参数计划
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_august_data():
    """加载8月份数据"""
    try:
        # 尝试加载8月份数据文件
        df = pd.read_csv('2025-08-03_22-09-59_Raw_Data.csv')
        
        # 筛选8月份数据
        august_data = df[df['date_time'].str.contains('2025/8', na=False)]
        
        print(f"成功加载8月份数据，共{len(august_data)}条记录")
        return august_data
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def analyze_august_results(df):
    """分析8月份测试结果"""
    print("\n" + "="*60)
    print("8月份2025年测试结果分析")
    print("="*60)
    
    if df is None or len(df) == 0:
        print("没有找到8月份数据")
        return
    
    # 基本统计信息
    print(f"\n1. 基本统计信息:")
    print(f"   总测试数量: {len(df)}")
    print(f"   测试日期范围: {df['date_time'].min()} 到 {df['date_time'].max()}")
    
    # 按组号分析
    print(f"\n2. 按组号分析:")
    group_counts = df['group#'].value_counts()
    print("   组号分布:")
    for group, count in group_counts.items():
        print(f"   {group}: {count}个样品")
    
    # 焊接质量分析
    print(f"\n3. 焊接质量分析:")
    weld_quality = df['weld_failure_type'].value_counts()
    print("   失效类型分布:")
    for failure_type, count in weld_quality.items():
        percentage = (count / len(df)) * 100
        print(f"   {failure_type}: {count}个 ({percentage:.1f}%)")
    
    # 泄漏测试结果
    print(f"\n4. 泄漏测试结果:")
    leakage_results = df['leakage'].value_counts()
    print("   泄漏测试结果:")
    for result, count in leakage_results.items():
        percentage = (count / len(df)) * 100
        print(f"   {result}: {count}个 ({percentage:.1f}%)")
    
    # 焊接高度分析
    print(f"\n5. 焊接高度分析:")
    height_data = df['post_weld_disk_holder_height'].dropna()
    if len(height_data) > 0:
        print(f"   平均高度: {height_data.mean():.3f} mm")
        print(f"   高度范围: {height_data.min():.3f} - {height_data.max():.3f} mm")
        print(f"   标准差: {height_data.std():.3f} mm")
    
    return df

def verify_parameter_plan(df):
    """审核参数计划"""
    print("\n" + "="*60)
    print("参数计划审核")
    print("="*60)
    
    # 定义计划参数
    planned_params = {
        'A': {'electrode_pressure': 44, 's1_percent_current': 97, 's1_hold': 6, 's1_cool': 0, 'repeats': 2},
        'B': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 5, 's1_cool': 0, 'repeats': 1},
        'C': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 5, 's1_cool': 1, 'repeats': 1},
        'D': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 7, 's1_cool': 0, 'repeats': 1},
        'E': {'electrode_pressure': 45, 's1_percent_current': 99, 's1_hold': 7, 's1_cool': 1, 'repeats': 1},
        'F': {'electrode_pressure': 45, 's1_percent_current': 98, 's1_hold': 5, 's1_cool': 0, 'repeats': 2},
        'G': {'electrode_pressure': 45, 's1_percent_current': 98, 's1_hold': 7, 's1_cool': 0, 'repeats': 2}
    }
    
    print("\n1. 计划参数设置:")
    for group, params in planned_params.items():
        print(f"   组{group}: 电极压力={params['electrode_pressure']}psi, "
              f"S1电流={params['s1_percent_current']}%, "
              f"S1保持时间={params['s1_hold']}c, "
              f"S1冷却={params['s1_cool']}c, "
              f"重复次数={params['repeats']}")
    
    # 分析实际执行情况
    print(f"\n2. 实际执行情况分析:")
    
    # 按组号分析实际参数
    for group in planned_params.keys():
        group_data = df[df['group#'].str.contains(group, na=False)]
        if len(group_data) > 0:
            print(f"\n   组{group}实际执行情况:")
            print(f"   实际样品数: {len(group_data)}")
            
            # 检查参数一致性
            if len(group_data) > 0:
                actual_pressure = group_data['electrode_pressure'].iloc[0]
                actual_current = group_data['s1_percent_current'].iloc[0]
                actual_hold = group_data['s1_hold'].iloc[0]
                actual_cool = group_data['s1_cool'].iloc[0]
                
                planned = planned_params[group]
                
                print(f"   电极压力: 计划{planned['electrode_pressure']}psi, 实际{actual_pressure}psi "
                      f"({'✓' if actual_pressure == planned['electrode_pressure'] else '✗'})")
                print(f"   S1电流: 计划{planned['s1_percent_current']}%, 实际{actual_current}% "
                      f"({'✓' if actual_current == planned['s1_percent_current'] else '✗'})")
                print(f"   S1保持时间: 计划{planned['s1_hold']}c, 实际{actual_hold}c "
                      f"({'✓' if actual_hold == planned['s1_hold'] else '✗'})")
                print(f"   S1冷却: 计划{planned['s1_cool']}c, 实际{actual_cool}c "
                      f"({'✓' if actual_cool == planned['s1_cool'] else '✗'})")
                
                # 质量分析
                pass_rate = (group_data['leakage'] == 'Pass').sum() / len(group_data) * 100
                print(f"   通过率: {pass_rate:.1f}%")
        else:
            print(f"\n   组{group}: 未找到数据")

def analyze_fixed_parameters(df):
    """分析固定参数设置"""
    print("\n" + "="*60)
    print("固定参数设置分析")
    print("="*60)
    
    # 定义固定参数
    fixed_params = {
        'S1阶段': {
            's1_squeeze': 15,
            's1_weld_heat': 1,
            's1_off': 0,
            's1_impulses': 1,
            's1_valve_mode': 7,
            's1_cycle_mode': 2,
            's1_current_mode': 0,
            's1_slope_count': 0,
            'electrode_height': 210.5
        },
        'S2阶段': {
            's2_squeeze': 5,
            's2_weld_heat': 1,
            's2_off': 0,
            's2_hold': 20,
            's2_cool': 1,
            's2_impulses': 1,
            's2_percent_current': 99,
            's2_current_mode': 0,
            's2_valve_mode': 7,
            's2_cycle_mode': 2,
            's2_slope_count': 0
        }
    }
    
    print("\n1. 固定参数设置:")
    for stage, params in fixed_params.items():
        print(f"\n   {stage}:")
        for param, value in params.items():
            print(f"     {param}: {value}")
    
    # 检查实际执行情况
    print(f"\n2. 固定参数执行情况检查:")
    
    # 检查S1阶段参数
    print(f"\n   S1阶段参数检查:")
    for param, planned_value in fixed_params['S1阶段'].items():
        if param in df.columns:
            actual_values = df[param].dropna().unique()
            if len(actual_values) > 0:
                actual_value = actual_values[0]
                status = "✓" if actual_value == planned_value else "✗"
                print(f"     {param}: 计划{planned_value}, 实际{actual_value} {status}")
    
    # 检查S2阶段参数
    print(f"\n   S2阶段参数检查:")
    for param, planned_value in fixed_params['S2阶段'].items():
        if param in df.columns:
            actual_values = df[param].dropna().unique()
            if len(actual_values) > 0:
                actual_value = actual_values[0]
                status = "✓" if actual_value == planned_value else "✗"
                print(f"     {param}: 计划{planned_value}, 实际{actual_value} {status}")

def generate_summary_report(df):
    """生成总结报告"""
    print("\n" + "="*60)
    print("总结报告")
    print("="*60)
    
    if df is None or len(df) == 0:
        print("没有数据可分析")
        return
    
    # 总体质量统计
    total_samples = len(df)
    pass_samples = (df['leakage'] == 'Pass').sum()
    fail_samples = total_samples - pass_samples
    pass_rate = (pass_samples / total_samples) * 100
    
    print(f"\n1. 总体质量统计:")
    print(f"   总样品数: {total_samples}")
    print(f"   通过样品数: {pass_samples}")
    print(f"   失败样品数: {fail_samples}")
    print(f"   通过率: {pass_rate:.1f}%")
    
    # 主要发现
    print(f"\n2. 主要发现:")
    
    # 检查是否有8月份的数据
    august_data = df[df['date_time'].str.contains('2025/8', na=False)]
    if len(august_data) > 0:
        print(f"   ✓ 发现{len(august_data)}条8月份测试数据")
        
        # 分析组号分布
        group_distribution = august_data['group#'].value_counts()
        print(f"   ✓ 测试组号分布: {dict(group_distribution)}")
        
        # 分析质量结果
        august_pass_rate = (august_data['leakage'] == 'Pass').sum() / len(august_data) * 100
        print(f"   ✓ 8月份测试通过率: {august_pass_rate:.1f}%")
        
        # 分析失效模式
        failure_modes = august_data['weld_failure_type'].value_counts()
        if len(failure_modes) > 0:
            print(f"   ✓ 主要失效模式: {dict(failure_modes)}")
    else:
        print("   ✗ 未发现8月份测试数据")
    
    # 建议
    print(f"\n3. 建议:")
    print("   - 继续监控8月份测试数据的质量趋势")
    print("   - 对比不同组号的参数设置效果")
    print("   - 分析固定参数设置的稳定性")
    print("   - 根据测试结果优化参数设置")

def main():
    """主函数"""
    print("G3P OP60 8月份2025年测试结果分析")
    print("="*60)
    
    # 加载数据
    df = load_august_data()
    
    # 分析8月份结果
    august_df = analyze_august_results(df)
    
    # 审核参数计划
    verify_parameter_plan(df)
    
    # 分析固定参数
    analyze_fixed_parameters(df)
    
    # 生成总结报告
    generate_summary_report(df)
    
    print("\n" + "="*60)
    print("分析完成")
    print("="*60)

if __name__ == "__main__":
    main() 