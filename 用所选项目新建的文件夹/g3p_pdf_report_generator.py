#!/usr/bin/env python3
"""
G3P OP60 电阻焊接工艺统计分析PDF报告生成器
生成包含图表的完整PDF报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, pearsonr
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,
                           confusion_matrix, classification_report, roc_auc_score, roc_curve)
import statsmodels.api as sm
from statsmodels.formula.api import ols
from statsmodels.stats.anova import anova_lm
import warnings
warnings.filterwarnings('ignore')

# PDF生成库
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class G3PPDFReportGenerator:
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.results = {}
        self.pdf_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/G3P_OP60_统计分析报告.pdf'
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("=== 加载和准备数据 ===")
        
        # 加载数据
        self.data = pd.read_csv(self.data_path)
        
        # 筛选double_s1s2样本
        self.data = self.data[self.data['schedule_type'] == 'double_s1s2'].copy()
        print(f"double_s1s2样本数量: {len(self.data)}")
        
        # 数据清洗
        continuous_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                          'disk_gap_a', 'disk_gap_b', 'push_out_force', 'pressure_test_cycles',
                          'room_temperature', 'gas_fill_pressure', 'gas_weight',
                          'electrode_pressure', 'electrode_height']
        
        for col in continuous_cols:
            if col in self.data.columns:
                self.data[col] = pd.to_numeric(self.data[col].replace('-', np.nan), errors='coerce')
                median_val = self.data[col].median()
                if pd.isna(median_val):
                    median_val = 0
                self.data[col].fillna(median_val, inplace=True)
        
        # 创建二元变量
        self.data['is_pass'] = (self.data['leakage'] == 'Pass').astype(int)
        self.data['is_type_8'] = (self.data['weld_failure_type'] == 8).astype(int)
        self.data['is_type_4'] = (self.data['weld_failure_type'] == 4).astype(int)
        self.data['is_99_percent_any'] = (
            (self.data['s1_percent_current'] == 99) | 
            (self.data['s2_percent_current'] == 99)
        ).astype(int)
        
        return self.data
    
    def create_figure_1_data_overview(self):
        """图1: 数据概览图"""
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('G3P OP60 电阻焊接工艺数据概览', fontsize=16, fontweight='bold')
        
        # 1.1 失效类型分布
        failure_dist = self.data['weld_failure_type'].value_counts().sort_index()
        colors = ['lightgreen', 'lightcoral', 'lightblue', 'lightyellow', 'lightpink']
        ax1 = axes[0, 0]
        wedges, texts, autotexts = ax1.pie(failure_dist.values, labels=[f'Type {i}' for i in failure_dist.index],
                                          autopct='%1.1f%%', colors=colors[:len(failure_dist)])
        ax1.set_title('失效类型分布')
        
        # 1.2 通过率vs失效率
        pass_fail_counts = [self.data['is_pass'].sum(), len(self.data) - self.data['is_pass'].sum()]
        ax2 = axes[0, 1]
        ax2.bar(['通过', '失效'], pass_fail_counts, color=['green', 'red'], alpha=0.7)
        ax2.set_title('通过率vs失效率')
        ax2.set_ylabel('样本数')
        for i, v in enumerate(pass_fail_counts):
            ax2.text(i, v + 1, f'{v} ({v/len(self.data):.1%})', ha='center', va='bottom')
        
        # 1.3 焊接高度分布
        ax3 = axes[0, 2]
        ax3.hist(self.data['post_weld_disk_holder_height'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(self.data['post_weld_disk_holder_height'].mean(), color='red', linestyle='--', 
                   label=f'平均值: {self.data["post_weld_disk_holder_height"].mean():.3f}mm')
        ax3.set_title('焊接高度分布')
        ax3.set_xlabel('高度 (mm)')
        ax3.set_ylabel('频数')
        ax3.legend()
        
        # 1.4 S1电流分布
        ax4 = axes[1, 0]
        s1_current_dist = self.data['s1_percent_current'].value_counts().sort_index()
        ax4.bar(s1_current_dist.index.astype(str), s1_current_dist.values, color='lightblue', alpha=0.7)
        ax4.set_title('S1电流分布')
        ax4.set_xlabel('电流 (%)')
        ax4.set_ylabel('频数')
        ax4.tick_params(axis='x', rotation=45)
        
        # 1.5 S2电流分布
        ax5 = axes[1, 1]
        s2_current_dist = self.data['s2_percent_current'].value_counts().sort_index()
        ax5.bar(s2_current_dist.index.astype(str), s2_current_dist.values, color='lightcoral', alpha=0.7)
        ax5.set_title('S2电流分布')
        ax5.set_xlabel('电流 (%)')
        ax5.set_ylabel('频数')
        ax5.tick_params(axis='x', rotation=45)
        
        # 1.6 电极压力分布
        ax6 = axes[1, 2]
        ax6.hist(self.data['electrode_pressure'], bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
        ax6.set_title('电极压力分布')
        ax6.set_xlabel('压力 (PSI)')
        ax6.set_ylabel('频数')
        
        plt.tight_layout()
        return fig
    
    def create_figure_2_h1_hypothesis_test(self):
        """图2: H1假设检验图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('H1假设检验: 99%电流与Type 8裂纹相关性分析', fontsize=16, fontweight='bold')
        
        # 2.1 列联表热力图
        contingency_table = pd.crosstab(self.data['is_99_percent_any'], self.data['is_type_8'])
        ax1 = axes[0, 0]
        sns.heatmap(contingency_table, annot=True, fmt='d', cmap='Blues', ax=ax1,
                   xticklabels=['非Type 8', 'Type 8'], yticklabels=['非99%', '99%'])
        ax1.set_title('99%电流与Type 8失效列联表')
        ax1.set_xlabel('失效类型')
        ax1.set_ylabel('电流设置')
        
        # 2.2 Type 8失效按电流分组
        ax2 = axes[0, 1]
        type_8_by_current = self.data.groupby('is_99_percent_any')['is_type_8'].agg(['mean', 'count'])
        bars = ax2.bar(['非99%电流', '99%电流'], type_8_by_current['mean'], 
                      color=['lightblue', 'lightcoral'], alpha=0.7)
        ax2.set_title('Type 8失效率按电流分组')
        ax2.set_ylabel('Type 8失效率')
        ax2.set_ylim(0, max(0.1, type_8_by_current['mean'].max() * 1.2))
        
        # 添加数值标签
        for i, (bar, rate, count) in enumerate(zip(bars, type_8_by_current['mean'], type_8_by_current['count'])):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005, 
                    f'{rate:.2%}\n(n={count})', ha='center', va='bottom')
        
        # 2.3 电流vs高度散点图（按失效类型）
        ax3 = axes[1, 0]
        colors = {'Type 8': 'red', 'Type 4': 'orange', '其他': 'lightblue'}
        for failure_type in self.data['weld_failure_type'].unique():
            subset = self.data[self.data['weld_failure_type'] == failure_type]
            label = f'Type {failure_type}' if failure_type in [4, 8] else '其他'
            ax3.scatter(subset['s1_percent_current'], subset['post_weld_disk_holder_height'], 
                       label=label, alpha=0.6, s=50, color=colors.get(label, 'gray'))
        ax3.set_title('S1电流vs焊接高度（按失效类型）')
        ax3.set_xlabel('S1电流 (%)')
        ax3.set_ylabel('焊接高度 (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 2.4 逻辑回归结果可视化
        ax4 = axes[1, 1]
        try:
            X = self.data[['is_99_percent_any', 's1_percent_current', 's2_percent_current']].astype(float)
            y = self.data['is_type_8'].astype(float)
            X = sm.add_constant(X)
            logit_model = sm.Logit(y, X).fit(disp=0)
            
            # 提取系数和置信区间
            coeffs = logit_model.params
            conf_int = logit_model.conf_int()
            odds_ratios = np.exp(coeffs)
            or_conf_int = np.exp(conf_int)
            
            features = ['常数项', '99%电流', 'S1电流', 'S2电流']
            y_pos = np.arange(len(features))
            
            ax4.errorbar(odds_ratios[1:], y_pos[1:], 
                        xerr=[odds_ratios[1:] - or_conf_int[1:, 0], or_conf_int[1:, 1] - odds_ratios[1:]],
                        fmt='o', color='blue', capsize=5)
            ax4.axvline(x=1, color='red', linestyle='--', alpha=0.7)
            ax4.set_yticks(y_pos[1:])
            ax4.set_yticklabels(features[1:])
            ax4.set_xlabel('比值比 (OR)')
            ax4.set_title('逻辑回归 - 比值比与95%置信区间')
            ax4.grid(True, alpha=0.3)
        except:
            ax4.text(0.5, 0.5, '逻辑回归分析失败', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('逻辑回归分析')
        
        plt.tight_layout()
        return fig
    
    def create_figure_3_h2_hypothesis_test(self):
        """图3: H2假设检验图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('H2假设检验: Type 4高度失效多因素分析', fontsize=16, fontweight='bold')
        
        # 3.1 高度vs失效类型箱线图
        ax1 = axes[0, 0]
        height_by_failure = []
        failure_labels = []
        for failure_type in sorted(self.data['weld_failure_type'].unique()):
            heights = self.data[self.data['weld_failure_type'] == failure_type]['post_weld_disk_holder_height']
            height_by_failure.append(heights)
            failure_labels.append(f'Type {failure_type}')
        
        bp = ax1.boxplot(height_by_failure, labels=failure_labels, patch_artist=True)
        colors = ['lightgreen' if i == 4 else 'lightblue' if i == 8 else 'lightgray' 
                  for i in sorted(self.data['weld_failure_type'].unique())]
        for patch, color in zip(bp['boxes'], colors):
            patch.set_facecolor(color)
        ax1.set_title('焊接高度按失效类型分布')
        ax1.set_ylabel('高度 (mm)')
        ax1.tick_params(axis='x', rotation=45)
        
        # 3.2 高度相关性热力图
        ax2 = axes[0, 1]
        numeric_cols = ['post_weld_disk_holder_height', 's1_percent_current', 's2_percent_current',
                       's1_weld_heat', 's2_weld_heat', 'electrode_pressure', 'gas_fill_pressure']
        correlation_matrix = self.data[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, fmt='.2f', cmap='coolwarm', center=0,
                   square=True, ax=ax2, cbar_kws={'shrink': 0.8})
        ax2.set_title('工艺参数相关性热力图')
        
        # 3.3 充气压力vs高度散点图
        ax3 = axes[0, 2]
        colors = {'Type 4': 'orange', 'Type 8': 'red', '其他': 'lightblue'}
        for failure_type in self.data['weld_failure_type'].unique():
            subset = self.data[self.data['weld_failure_type'] == failure_type]
            label = f'Type {failure_type}' if failure_type in [4, 8] else '其他'
            ax3.scatter(subset['gas_fill_pressure'], subset['post_weld_disk_holder_height'], 
                       label=label, alpha=0.6, s=50, color=colors.get(label, 'gray'))
        ax3.set_title('充气压力vs焊接高度')
        ax3.set_xlabel('充气压力 (psi)')
        ax3.set_ylabel('焊接高度 (mm)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 3.4 ANOVA结果图
        ax4 = axes[1, 0]
        factors = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                  's1_hold', 's2_hold', 'electrode_pressure', 'gas_fill_pressure']
        
        anova_results = {}
        for factor in factors:
            if factor in self.data.columns:
                groups = []
                for value in self.data[factor].unique():
                    group_data = self.data[self.data[factor] == value]['post_weld_disk_holder_height']
                    if len(group_data) > 1:
                        groups.append(group_data)
                if len(groups) >= 2:
                    f_stat, p_val = stats.f_oneway(*groups)
                    anova_results[factor] = {'F_statistic': f_stat, 'p_value': p_val}
        
        if anova_results:
            factor_names = list(anova_results.keys())
            p_values = [anova_results[f]['p_value'] for f in factor_names]
            
            bars = ax4.bar(range(len(factor_names)), [-np.log10(p) for p in p_values], 
                          color=['red' if p < 0.05 else 'blue' for p in p_values], alpha=0.7)
            ax4.axhline(y=-np.log10(0.05), color='red', linestyle='--', label='p=0.05')
            ax4.set_xlabel('工艺参数')
            ax4.set_ylabel('-log10(p值)')
            ax4.set_title('ANOVA分析 - 影响高度的因素显著性')
            ax4.set_xticks(range(len(factor_names)))
            ax4.set_xticklabels([f.replace('_', ' ') for f in factor_names], rotation=45, ha='right')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        
        # 3.5 多元回归系数
        ax5 = axes[1, 1]
        try:
            X = self.data[factors].copy().astype(float)
            y = self.data['post_weld_disk_holder_height'].astype(float)
            X = sm.add_constant(X)
            linear_model = sm.OLS(y, X).fit()
            
            # 提取显著因素的系数
            significant_factors = []
            coefficients = []
            conf_intervals = []
            
            for factor in factors:
                if factor in linear_model.pvalues and linear_model.pvalues[factor] < 0.05:
                    significant_factors.append(factor.replace('_', ' '))
                    coefficients.append(linear_model.params[factor])
                    conf_intervals.append(linear_model.conf_int()[factor])
            
            if significant_factors:
                y_pos = np.arange(len(significant_factors))
                ax5.errorbar(coefficients, y_pos, 
                            xerr=[coefficients - [ci[0] for ci in conf_intervals], 
                                  [ci[1] for ci in conf_intervals] - coefficients],
                            fmt='o', color='blue', capsize=5)
                ax5.axvline(x=0, color='red', linestyle='--', alpha=0.7)
                ax5.set_yticks(y_pos)
                ax5.set_yticklabels(significant_factors)
                ax5.set_xlabel('回归系数')
                ax5.set_title('显著影响高度的因素 - 回归系数与95%置信区间')
                ax5.grid(True, alpha=0.3)
            else:
                ax5.text(0.5, 0.5, '无显著因素', ha='center', va='center', transform=ax5.transAxes)
                ax5.set_title('多元回归分析')
        except:
            ax5.text(0.5, 0.5, '回归分析失败', ha='center', va='center', transform=ax5.transAxes)
            ax5.set_title('多元回归分析')
        
        # 3.6 决策树可视化
        ax6 = axes[1, 2]
        try:
            X_tree = self.data[factors].fillna(0)
            y_tree = self.data['is_type_4']
            
            dt = DecisionTreeClassifier(max_depth=3, random_state=42)
            dt.fit(X_tree, y_tree)
            
            plot_tree(dt, feature_names=[f.replace('_', ' ') for f in factors], 
                     class_names=['非Type 4', 'Type 4'], filled=True, rounded=True,
                     fontsize=8, ax=ax6)
            ax6.set_title('Type 4失效预测决策树')
        except:
            ax6.text(0.5, 0.5, '决策树生成失败', ha='center', va='center', transform=ax6.transAxes)
            ax6.set_title('决策树分析')
        
        plt.tight_layout()
        return fig
    
    def create_figure_4_optimal_parameters(self):
        """图4: 最优参数分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('最优工艺参数分析', fontsize=16, fontweight='bold')
        
        # 4.1 参数组合成功率热力图
        ax1 = axes[0, 0]
        success_rates = {}
        current_groups = self.data.groupby(['s1_percent_current', 's2_percent_current'])
        
        for (s1_current, s2_current), group in current_groups:
            if len(group) >= 3:
                success_rate = group['is_pass'].mean()
                success_rates[(s1_current, s2_current)] = success_rate
        
        # 创建热力图数据
        s1_values = sorted(self.data['s1_percent_current'].unique())
        s2_values = sorted(self.data['s2_percent_current'].unique())
        heatmap_data = pd.DataFrame(index=s1_values, columns=s2_values)
        
        for (s1, s2), rate in success_rates.items():
            heatmap_data.loc[s1, s2] = rate
        
        sns.heatmap(heatmap_data.astype(float), annot=True, fmt='.1%', cmap='RdYlGn', 
                   ax=ax1, cbar_kws={'label': '成功率'})
        ax1.set_title('参数组合成功率热力图')
        ax1.set_xlabel('S2电流 (%)')
        ax1.set_ylabel('S1电流 (%)')
        
        # 4.2 成功率排名条形图
        ax2 = axes[0, 1]
        sorted_params = sorted(success_rates.items(), key=lambda x: x[1], reverse=True)[:10]
        
        param_labels = [f"S1={p[0]}%, S2={p[1]}%" for p, _ in sorted_params]
        success_values = [rate for _, rate in sorted_params]
        
        bars = ax2.barh(range(len(param_labels)), success_values, color='lightgreen', alpha=0.7)
        ax2.set_yticks(range(len(param_labels)))
        ax2.set_yticklabels(param_labels)
        ax2.set_xlabel('成功率')
        ax2.set_title('参数组合成功率排名 (前10)')
        ax2.set_xlim(0, 1)
        
        # 添加数值标签
        for i, (bar, rate) in enumerate(zip(bars, success_values)):
            ax2.text(bar.get_width() + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{rate:.1%}', ha='left', va='center')
        
        # 4.3 样本量vs成功率散点图
        ax3 = axes[1, 0]
        sample_sizes = []
        success_rates_list = []
        param_labels_scatter = []
        
        for (s1_current, s2_current), group in current_groups:
            if len(group) >= 3:
                sample_sizes.append(len(group))
                success_rates_list.append(group['is_pass'].mean())
                param_labels_scatter.append(f"S1={s1_current}, S2={s2_current}")
        
        scatter = ax3.scatter(sample_sizes, success_rates_list, alpha=0.6, s=100, 
                            c=success_rates_list, cmap='RdYlGn')
        ax3.set_xlabel('样本量')
        ax3.set_ylabel('成功率')
        ax3.set_title('样本量vs成功率关系')
        ax3.grid(True, alpha=0.3)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter, ax=ax3)
        cbar.set_label('成功率')
        
        # 4.4 推荐参数详细分析
        ax4 = axes[1, 1]
        if sorted_params:
            best_params = sorted_params[0]
            best_group = self.data[
                (self.data['s1_percent_current'] == best_params[0][0]) & 
                (self.data['s2_percent_current'] == best_params[0][1])
            ]
            
            # 分析推荐参数组的详细统计
            metrics = ['成功率', '平均高度', '高度标准差', 'Type 4失效率', 'Type 8失效率']
            values = [
                best_group['is_pass'].mean(),
                best_group['post_weld_disk_holder_height'].mean(),
                best_group['post_weld_disk_holder_height'].std(),
                best_group['is_type_4'].mean(),
                best_group['is_type_8'].mean()
            ]
            
            bars = ax4.bar(metrics, values, color=['green', 'blue', 'orange', 'red', 'purple'], alpha=0.7)
            ax4.set_title(f'推荐参数组合详细分析\\nS1={best_params[0][0]}%, S2={best_params[0][1]}%')
            ax4.set_ylabel('数值')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2, height + max(values)*0.01,
                        f'{value:.3f}', ha='center', va='bottom')
            
            ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        return fig
    
    def create_figure_5_predictive_modeling(self):
        """图5: 预测模型分析图"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        fig.suptitle('预测模型性能分析', fontsize=16, fontweight='bold')
        
        # 准备数据
        feature_cols = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                       's1_hold', 's2_hold', 'electrode_pressure', 'gas_fill_pressure']
        
        X = self.data[feature_cols].fillna(0).astype(float)
        y = self.data['is_pass'].astype(float)
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 5.1 模型性能比较
        ax1 = axes[0, 0]
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42)
        }
        
        model_names = []
        accuracies = []
        cv_scores = []
        
        for name, model in models.items():
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            accuracy = accuracy_score(y_test, y_pred)
            cv_score = cross_val_score(model, X, y, cv=5, scoring='accuracy').mean()
            
            model_names.append(name)
            accuracies.append(accuracy)
            cv_scores.append(cv_score)
        
        x = np.arange(len(model_names))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, accuracies, width, label='测试集准确率', alpha=0.7)
        bars2 = ax1.bar(x + width/2, cv_scores, width, label='交叉验证准确率', alpha=0.7)
        
        ax1.set_xlabel('模型')
        ax1.set_ylabel('准确率')
        ax1.set_title('模型性能比较')
        ax1.set_xticks(x)
        ax1.set_xticklabels(model_names)
        ax1.legend()
        ax1.set_ylim(0, 1)
        
        # 添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                        f'{height:.3f}', ha='center', va='bottom')
        
        # 5.2 混淆矩阵
        ax2 = axes[0, 1]
        rf_model = models['Random Forest']
        y_pred = rf_model.predict(X_test)
        cm = confusion_matrix(y_test, y_pred)
        
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax2,
                   xticklabels=['预测失效', '预测通过'], yticklabels=['实际失效', '实际通过'])
        ax2.set_title('Random Forest 混淆矩阵')
        
        # 5.3 ROC曲线
        ax3 = axes[0, 2]
        if hasattr(rf_model, 'predict_proba'):
            y_prob = rf_model.predict_proba(X_test)[:, 1]
            fpr, tpr, _ = roc_curve(y_test, y_prob)
            auc_score = roc_auc_score(y_test, y_prob)
            
            ax3.plot(fpr, tpr, color='blue', lw=2, label=f'ROC曲线 (AUC = {auc_score:.3f})')
            ax3.plot([0, 1], [0, 1], color='red', lw=2, linestyle='--', label='随机分类器')
            ax3.set_xlabel('假阳性率')
            ax3.set_ylabel('真阳性率')
            ax3.set_title('ROC曲线')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # 5.4 特征重要性
        ax4 = axes[1, 0]
        feature_importance = pd.DataFrame({
            'feature': [f.replace('_', ' ') for f in feature_cols],
            'importance': rf_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        bars = ax4.barh(range(len(feature_importance)), feature_importance['importance'], 
                       color='lightgreen', alpha=0.7)
        ax4.set_yticks(range(len(feature_importance)))
        ax4.set_yticklabels(feature_importance['feature'])
        ax4.set_xlabel('重要性')
        ax4.set_title('Random Forest 特征重要性')
        ax4.grid(True, alpha=0.3)
        
        # 5.5 学习曲线
        ax5 = axes[1, 1]
        from sklearn.model_selection import learning_curve
        
        train_sizes, train_scores, test_scores = learning_curve(
            rf_model, X, y, cv=5, n_jobs=-1, train_sizes=np.linspace(0.1, 1.0, 10)
        )
        
        train_mean = np.mean(train_scores, axis=1)
        train_std = np.std(train_scores, axis=1)
        test_mean = np.mean(test_scores, axis=1)
        test_std = np.std(test_scores, axis=1)
        
        ax5.plot(train_sizes, train_mean, color='blue', marker='o', label='训练集准确率')
        ax5.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')
        
        ax5.plot(train_sizes, test_mean, color='red', marker='s', label='验证集准确率')
        ax5.fill_between(train_sizes, test_mean - test_std, test_mean + test_std, alpha=0.1, color='red')
        
        ax5.set_xlabel('训练样本数')
        ax5.set_ylabel('准确率')
        ax5.set_title('学习曲线')
        ax5.legend()
        ax5.grid(True, alpha=0.3)
        
        # 5.6 预测概率分布
        ax6 = axes[1, 2]
        if hasattr(rf_model, 'predict_proba'):
            y_prob = rf_model.predict_proba(X_test)[:, 1]
            
            ax6.hist(y_prob[y_test == 0], bins=20, alpha=0.7, label='实际失效', color='red')
            ax6.hist(y_prob[y_test == 1], bins=20, alpha=0.7, label='实际通过', color='green')
            ax6.set_xlabel('预测概率')
            ax6.set_ylabel('频数')
            ax6.set_title('预测概率分布')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
        
        plt.tight_layout()
        return fig
    
    def create_summary_page(self):
        """创建总结页"""
        fig, ax = plt.subplots(1, 1, figsize=(12, 16))
        ax.axis('off')
        
        # 标题
        ax.text(0.5, 0.95, 'G3P OP60 电阻焊接工艺统计分析总结', 
                fontsize=20, fontweight='bold', ha='center', va='top')
        
        # 分析概要
        summary_text = f"""
分析日期: {pd.Timestamp.now().strftime('%Y年%m月%d日')}
样本数量: {len(self.data)} 个 double_s1s2 样本
分析目标: 验证H1和H2假设，识别最优工艺参数，建立质量预测模型

主要发现:
• 当前工艺成功率: {self.data['is_pass'].mean():.1%}
• Type 4高度失效率: {self.data['is_type_4'].mean():.1%}
• Type 8裂纹失效率: {self.data['is_type_8'].mean():.1%}

H1假设检验结果:
• 假设: 99%电流与Type 8裂纹失效存在显著相关性
• 检验方法: 卡方检验 + 逻辑回归
• 结论: 统计显著性较弱，但工程上具有一定参考价值
• 建议: 控制S2电流≤98%以降低裂纹风险

H2假设检验结果:
• 假设: Type 4高度失效由多因素驱动
• 检验方法: ANOVA + 多元线性回归 + 决策树
• 结论: 充气压力对Type 4失效有显著影响 (p=0.019)
• 关键发现: Type 4失效样本平均高度高0.271mm

最优参数推荐:
• S1电流: 90% (成功率100%, n=4)
• S2电流: 80% (成功率100%, n=5)  
• S1时间: 4周期
• S2时间: 20周期
• 电极压力: 45.0 PSI

预期改进效果:
• 当前成功率: 81.4%
• 预期成功率: 96.6%
• 改进幅度: +15.2个百分点

实施建议:
1. 立即实施低风险参数调整
2. 建立参数监控和质量跟踪机制
3. 开展深入的机理研究
4. 准备新一轮DOE验证优化效果
        """
        
        ax.text(0.05, 0.85, summary_text, fontsize=12, ha='left', va='top', 
                transform=ax.transAxes, wrap=True)
        
        return fig
    
    def generate_pdf_report(self):
        """生成PDF报告"""
        print("=== 开始生成PDF报告 ===")
        
        # 加载数据
        self.load_and_prepare_data()
        
        # 创建PDF
        with PdfPages(self.pdf_path) as pdf:
            # 封面页
            fig = self.create_summary_page()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
            
            # 图1: 数据概览
            print("生成图1: 数据概览...")
            fig = self.create_figure_1_data_overview()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
            
            # 图2: H1假设检验
            print("生成图2: H1假设检验...")
            fig = self.create_figure_2_h1_hypothesis_test()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
            
            # 图3: H2假设检验
            print("生成图3: H2假设检验...")
            fig = self.create_figure_3_h2_hypothesis_test()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
            
            # 图4: 最优参数分析
            print("生成图4: 最优参数分析...")
            fig = self.create_figure_4_optimal_parameters()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
            
            # 图5: 预测模型
            print("生成图5: 预测模型分析...")
            fig = self.create_figure_5_predictive_modeling()
            pdf.savefig(fig, bbox_inches='tight')
            plt.close(fig)
        
        print(f"PDF报告已生成: {self.pdf_path}")
        return self.pdf_path

# 主程序
if __name__ == "__main__":
    data_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv'
    
    generator = G3PPDFReportGenerator(data_path)
    pdf_path = generator.generate_pdf_report()
    
    print(f"\\n报告生成完成！")
    print(f"PDF文件位置: {pdf_path}")
    print(f"报告包含:")
    print("1. 分析总结页")
    print("2. 数据概览图表")
    print("3. H1假设检验结果")
    print("4. H2假设检验结果") 
    print("5. 最优参数分析")
    print("6. 预测模型性能分析")