#!/usr/bin/env python3
"""
G3P OP60电阻焊接工艺统计分析报告生成器
生成包含图表的HTML格式技术报告
"""

import markdown
import base64
import os
from datetime import datetime

def image_to_base64(image_path):
    """将图片转换为base64编码"""
    try:
        with open(image_path, "rb") as img_file:
            return base64.b64encode(img_file.read()).decode('utf-8')
    except FileNotFoundError:
        return None

def generate_html_report():
    """生成HTML格式的技术报告"""
    
    # 读取markdown报告
    with open('G3P_OP60_专业技术报告.md', 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 图片文件列表
    image_files = [
        'g3p_data_exploration.png',
        'g3p_h1_hypothesis_verification.png', 
        'g3p_h2_hypothesis_verification.png',
        'g3p_parameter_optimization.png'
    ]
    
    # 将图片转换为base64并替换markdown中的图片链接
    for img_file in image_files:
        if os.path.exists(img_file):
            img_base64 = image_to_base64(img_file)
            if img_base64:
                # 替换markdown中的图片链接
                md_content = md_content.replace(
                    f'![{img_file.replace(".png", "").replace("_", " ").title()}]({img_file})',
                    f'<img src="data:image/png;base64,{img_base64}" alt="{img_file}" style="max-width: 100%; height: auto; margin: 20px 0; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">'
                )
    
    # 转换markdown为HTML
    md = markdown.Markdown(extensions=['tables', 'toc', 'codehilite'])
    html_body = md.convert(md_content)
    
    # 创建完整的HTML文档
    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>G3P OP60电阻焊接工艺统计分析专业技术报告</title>
    <style>
        body {{
            font-family: 'Times New Roman', '宋体', serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #fff;
        }}
        
        h1 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            font-size: 2.5em;
            text-align: center;
            margin-bottom: 30px;
        }}
        
        h2 {{
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
            font-size: 1.8em;
            margin-top: 40px;
            margin-bottom: 20px;
        }}
        
        h3 {{
            color: #2c3e50;
            font-size: 1.4em;
            margin-top: 30px;
            margin-bottom: 15px;
        }}
        
        h4 {{
            color: #34495e;
            font-size: 1.2em;
            margin-top: 25px;
            margin-bottom: 10px;
        }}
        
        p {{
            text-align: justify;
            margin-bottom: 15px;
        }}
        
        ul, ol {{
            margin-bottom: 15px;
            padding-left: 30px;
        }}
        
        li {{
            margin-bottom: 8px;
        }}
        
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            font-size: 0.9em;
        }}
        
        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}
        
        th {{
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }}
        
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        
        .executive-summary {{
            background-color: #ecf0f1;
            padding: 20px;
            border-left: 5px solid #3498db;
            margin: 20px 0;
            border-radius: 5px;
        }}
        
        .key-finding {{
            background-color: #d5f4e6;
            padding: 15px;
            border-left: 5px solid #27ae60;
            margin: 15px 0;
            border-radius: 5px;
        }}
        
        .warning {{
            background-color: #fdf2e9;
            padding: 15px;
            border-left: 5px solid #e67e22;
            margin: 15px 0;
            border-radius: 5px;
        }}
        
        .recommendation {{
            background-color: #eaf2f8;
            padding: 15px;
            border-left: 5px solid #2980b9;
            margin: 15px 0;
            border-radius: 5px;
        }}
        
        code {{
            background-color: #f4f4f4;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }}
        
        pre {{
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }}
        
        .toc {{
            background-color: #f8f9fa;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            margin: 20px 0;
        }}
        
        .toc h2 {{
            margin-top: 0;
            color: #495057;
        }}
        
        .toc ul {{
            list-style-type: none;
            padding-left: 0;
        }}
        
        .toc li {{
            margin-bottom: 5px;
        }}
        
        .toc a {{
            text-decoration: none;
            color: #007bff;
        }}
        
        .toc a:hover {{
            text-decoration: underline;
        }}
        
        .page-break {{
            page-break-before: always;
        }}
        
        @media print {{
            body {{
                font-size: 12pt;
                line-height: 1.4;
            }}
            
            h1 {{
                font-size: 18pt;
            }}
            
            h2 {{
                font-size: 16pt;
                page-break-after: avoid;
            }}
            
            h3 {{
                font-size: 14pt;
                page-break-after: avoid;
            }}
            
            img {{
                max-width: 100% !important;
                page-break-inside: avoid;
            }}
            
            table {{
                page-break-inside: avoid;
            }}
            
            .page-break {{
                page-break-before: always;
            }}
        }}
        
        .header-info {{
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
        }}
        
        .header-info p {{
            margin: 5px 0;
            font-size: 1.1em;
        }}
        
        .chart-container {{
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background-color: #fafafa;
            border-radius: 10px;
        }}
        
        .chart-title {{
            font-size: 1.3em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
        }}
        
        .chart-description {{
            text-align: left;
            margin-top: 20px;
            padding: 15px;
            background-color: #fff;
            border-left: 4px solid #3498db;
            border-radius: 5px;
        }}
    </style>
</head>
<body>
    <div class="header-info">
        <h1>G3P OP60电阻焊接工艺统计分析专业技术报告</h1>
        <p><strong>项目名称:</strong> G3P OP60 Gas Fill Resistance Welding DOE</p>
        <p><strong>分析日期:</strong> {datetime.now().strftime('%Y年%m月%d日')}</p>
        <p><strong>报告版本:</strong> V1.0</p>
        <p><strong>数据源:</strong> NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv</p>
    </div>
    
    {html_body}
    
    <div class="page-break"></div>
    <footer style="text-align: center; margin-top: 50px; padding: 20px; border-top: 1px solid #ddd;">
        <p><strong>报告生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
        <p><strong>技术支持:</strong> AI统计分析系统</p>
        <p><strong>版权声明:</strong> 本报告为G3P OP60项目专用技术文档</p>
    </footer>
</body>
</html>
"""
    
    # 保存HTML文件
    with open('G3P_OP60_专业技术报告.html', 'w', encoding='utf-8') as f:
        f.write(html_template)
    
    print("HTML技术报告已生成：G3P_OP60_专业技术报告.html")
    print("\n使用说明：")
    print("1. 在浏览器中打开HTML文件")
    print("2. 使用浏览器的打印功能（Ctrl+P或Cmd+P）")
    print("3. 选择'保存为PDF'选项")
    print("4. 调整页面设置（建议A4纸张，边距适中）")
    print("5. 保存为PDF文件")
    
    return True

if __name__ == "__main__":
    generate_html_report()
