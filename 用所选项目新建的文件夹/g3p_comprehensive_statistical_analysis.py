#!/usr/bin/env python3
"""
G3P OP60 Comprehensive Statistical Analysis
基于"规划先行原则"的完整统计分析实现
验证H1: 99%电流与Type 8裂纹失效的相关性
验证H2: Type 4高度失效的多因素根因分析
识别最优工艺参数组合
建立质量预测模型
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, pearsonr, spearmanr
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.linear_model import LogisticRegression, LinearRegression
from sklearn.tree import DecisionTreeClassifier, plot_tree
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score,
                           confusion_matrix, classification_report, mean_squared_error,
                           r2_score, roc_auc_score, roc_curve)
from sklearn.feature_selection import SelectKBest, f_classif, chi2
from statsmodels.stats.anova import anova_lm
from statsmodels.formula.api import ols
import statsmodels.api as sm
import xgboost as xgb
import shap
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置样式
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class G3PStatisticalAnalysis:
    def __init__(self, data_path):
        """初始化统计分析类"""
        self.data_path = data_path
        self.raw_data = None
        self.double_s1s2_data = None
        self.processed_data = None
        self.scaler = StandardScaler()
        self.results = {}
        
    def load_data(self):
        """加载原始数据"""
        print("=== 加载数据 ===")
        self.raw_data = pd.read_csv(self.data_path)
        print(f"原始数据形状: {self.raw_data.shape}")
        print(f"列名: {list(self.raw_data.columns)}")
        return self.raw_data
    
    def extract_double_s1s2_samples(self):
        """提取140个double_s1s2样本"""
        print("\n=== 提取double_s1s2样本 ===")
        
        # 筛选double_s1s2样本
        self.double_s1s2_data = self.raw_data[
            self.raw_data['schedule_type'] == 'double_s1s2'
        ].copy()
        
        print(f"double_s1s2样本数量: {len(self.double_s1s2_data)}")
        print(f"样本日期范围: {self.double_s1s2_data['date_time'].min()} 到 {self.double_s1s2_data['date_time'].max()}")
        
        return self.double_s1s2_data
    
    def handle_missing_values(self):
        """处理缺失值和异常值"""
        print("\n=== 处理缺失值和异常值 ===")
        
        # 首先将字符串转换为数值
        continuous_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                          'disk_gap_a', 'disk_gap_b', 'push_out_force', 'pressure_test_cycles',
                          'room_temperature', 'gas_fill_pressure', 'gas_weight',
                          'electrode_pressure', 'electrode_height']
        
        for col in continuous_cols:
            if col in self.double_s1s2_data.columns:
                # 替换'-'为NaN，然后转换为数值
                self.double_s1s2_data[col] = pd.to_numeric(
                    self.double_s1s2_data[col].replace('-', np.nan), 
                    errors='coerce'
                )
        
        # 检查缺失值
        missing_values = self.double_s1s2_data.isnull().sum()
        print("缺失值统计:")
        print(missing_values[missing_values > 0])
        
        # 处理缺失值策略
        # 对于连续变量，用中位数填充
        for col in continuous_cols:
            if col in self.double_s1s2_data.columns:
                median_val = self.double_s1s2_data[col].median()
                if pd.isna(median_val):  # 如果全是NaN，用0填充
                    median_val = 0
                self.double_s1s2_data[col].fillna(median_val, inplace=True)
        
        # 对于分类变量，用众数填充
        categorical_cols = ['weld_failure_type', 'failure_details', 'leakage', 'push_out_mode',
                           'pressure_test_result']
        
        for col in categorical_cols:
            if col in self.double_s1s2_data.columns:
                mode_val = self.double_s1s2_data[col].mode()[0]
                self.double_s1s2_data[col].fillna(mode_val, inplace=True)
        
        # 处理异常值 - 使用IQR方法
        for col in continuous_cols:
            if col in self.double_s1s2_data.columns:
                Q1 = self.double_s1s2_data[col].quantile(0.25)
                Q3 = self.double_s1s2_data[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # 将异常值替换为边界值
                self.double_s1s2_data[col] = np.where(
                    self.double_s1s2_data[col] < lower_bound, lower_bound,
                    np.where(self.double_s1s2_data[col] > upper_bound, upper_bound,
                            self.double_s1s2_data[col])
                )
        
        print("缺失值和异常值处理完成")
        return self.double_s1s2_data
    
    def create_binary_variables(self):
        """创建二元变量用于统计分析"""
        print("\n=== 创建二元变量 ===")
        
        # 创建质量结果二元变量
        self.double_s1s2_data['is_pass'] = (self.double_s1s2_data['leakage'] == 'Pass').astype(int)
        self.double_s1s2_data['is_failed'] = (self.double_s1s2_data['leakage'] == 'Failed').astype(int)
        
        # 创建失效类型二元变量
        failure_types = [1, 4, 5, 6, 8]  # 主要失效类型
        for failure_type in failure_types:
            col_name = f'is_type_{failure_type}'
            self.double_s1s2_data[col_name] = (self.double_s1s2_data['weld_failure_type'] == failure_type).astype(int)
        
        # 创建99%电流二元变量
        self.double_s1s2_data['is_99_percent_s1'] = (self.double_s1s2_data['s1_percent_current'] == 99).astype(int)
        self.double_s1s2_data['is_99_percent_s2'] = (self.double_s1s2_data['s2_percent_current'] == 99).astype(int)
        self.double_s1s2_data['is_99_percent_any'] = (
            (self.double_s1s2_data['s1_percent_current'] == 99) | 
            (self.double_s1s2_data['s2_percent_current'] == 99)
        ).astype(int)
        
        # 创建高度相关变量
        height_threshold = 2.3  # 假设高度阈值
        self.double_s1s2_data['is_height_ok'] = (self.double_s1s2_data['post_weld_disk_holder_height'] <= height_threshold).astype(int)
        
        print("二元变量创建完成")
        print(f"通过率: {self.double_s1s2_data['is_pass'].mean():.2%}")
        print(f"Type 8失效比例: {self.double_s1s2_data['is_type_8'].mean():.2%}")
        print(f"Type 4失效比例: {self.double_s1s2_data['is_type_4'].mean():.2%}")
        print(f"99%电流使用比例: {self.double_s1s2_data['is_99_percent_any'].mean():.2%}")
        
        return self.double_s1s2_data
    
    def standardize_variables(self):
        """标准化连续变量"""
        print("\n=== 标准化连续变量 ===")
        
        # 选择需要标准化的连续变量
        continuous_vars = ['s1_squeeze', 's1_weld_heat', 's1_hold', 's1_off',
                          's2_squeeze', 's2_weld_heat', 's2_hold', 's2_off',
                          'post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b',
                          'disk_gap_a', 'disk_gap_b', 'electrode_pressure', 'electrode_height']
        
        # 只存在于数据中的变量
        available_vars = [var for var in continuous_vars if var in self.double_s1s2_data.columns]
        
        if available_vars:
            # 标准化
            self.double_s1s2_data[available_vars] = self.scaler.fit_transform(
                self.double_s1s2_data[available_vars]
            )
            print(f"已标准化 {len(available_vars)} 个连续变量")
        
        self.processed_data = self.double_s1s2_data.copy()
        return self.processed_data
    
    def descriptive_statistics(self):
        """描述性统计分析"""
        print("\n=== 描述性统计分析 ===")
        
        # 基本统计信息
        desc_stats = self.processed_data.describe()
        print("基本统计信息:")
        print(desc_stats)
        
        # 失效类型分布
        failure_dist = self.processed_data['weld_failure_type'].value_counts()
        print("\n失效类型分布:")
        print(failure_dist)
        
        # 泄漏测试结果分布
        leakage_dist = self.processed_data['leakage'].value_counts()
        print("\n泄漏测试结果分布:")
        print(leakage_dist)
        
        # 电流参数分布
        current_s1_dist = self.processed_data['s1_percent_current'].value_counts().sort_index()
        current_s2_dist = self.processed_data['s2_percent_current'].value_counts().sort_index()
        
        print("\nS1电流分布:")
        print(current_s1_dist)
        print("\nS2电流分布:")
        print(current_s2_dist)
        
        # 保存结果
        self.results['descriptive_stats'] = {
            'desc_stats': desc_stats,
            'failure_dist': failure_dist,
            'leakage_dist': leakage_dist,
            'current_s1_dist': current_s1_dist,
            'current_s2_dist': current_s2_dist
        }
        
        return self.results['descriptive_stats']
    
    def correlation_analysis(self):
        """相关性分析"""
        print("\n=== 相关性分析 ===")
        
        # 选择数值型变量进行相关性分析
        numeric_cols = self.processed_data.select_dtypes(include=[np.number]).columns
        
        # 计算相关系数矩阵
        correlation_matrix = self.processed_data[numeric_cols].corr()
        
        # 与质量结果的相关性
        quality_correlations = correlation_matrix[['is_pass', 'is_failed', 'is_type_8', 'is_type_4']].dropna()
        
        print("与质量结果的相关性:")
        print(quality_correlations)
        
        # 保存结果
        self.results['correlation_analysis'] = {
            'correlation_matrix': correlation_matrix,
            'quality_correlations': quality_correlations
        }
        
        return self.results['correlation_analysis']
    
    def h1_hypothesis_testing(self):
        """H1假设检验：99%电流与Type 8裂纹失效的相关性"""
        print("\n=== H1假设检验：99%电流与Type 8裂纹失效 ===")
        
        # 创建列联表
        contingency_table = pd.crosstab(
            self.processed_data['is_99_percent_any'],
            self.processed_data['is_type_8'],
            margins=True
        )
        
        print("列联表:")
        print(contingency_table)
        
        # 卡方检验
        chi2, p_value, dof, expected = chi2_contingency(contingency_table.iloc[:-1, :-1])
        
        print(f"\n卡方检验结果:")
        print(f"卡方值: {chi2:.4f}")
        print(f"P值: {p_value:.4f}")
        print(f"自由度: {dof}")
        
        # 效应量计算 (Cramer's V)
        n = contingency_table.sum().sum()
        phi = np.sqrt(chi2 / n)
        cramers_v = phi / np.sqrt(min(contingency_table.shape) - 2)
        
        print(f"Cramer's V效应量: {cramers_v:.4f}")
        
        # 逻辑回归分析
        X = self.processed_data[['is_99_percent_any', 's1_percent_current', 's2_percent_current']].copy()
        X = X.fillna(0)  # 处理缺失值
        y = self.processed_data['is_type_8']
        
        # 确保数据类型正确
        X = X.astype(float)
        y = y.astype(float)
        
        # 添加常数项
        X = sm.add_constant(X)
        
        # 逻辑回归
        try:
            logit_model = sm.Logit(y, X)
            result = logit_model.fit(disp=0)
        except Exception as e:
            print(f"逻辑回归失败: {e}")
            result = None
        
        if result is not None:
            print("\n逻辑回归结果:")
            print(result.summary())
            
            # 计算OR值和置信区间
            odds_ratios = np.exp(result.params)
            conf_int = np.exp(result.conf_int())
            conf_int.columns = ['95% CI Lower', '95% CI Upper']
            
            print("\n比值比(OR)和95%置信区间:")
            print(pd.DataFrame({'OR': odds_ratios, **conf_int}))
        else:
            print("逻辑回归无法完成，跳过相关分析")
        
        # 保存结果
        self.results['h1_testing'] = {
            'contingency_table': contingency_table,
            'chi2': chi2,
            'p_value': p_value,
            'cramers_v': cramers_v,
            'logistic_regression': result,
            'odds_ratios': odds_ratios,
            'confidence_intervals': conf_int
        }
        
        return self.results['h1_testing']
    
    def h2_hypothesis_testing(self):
        """H2假设检验：Type 4高度失效的多因素根因分析"""
        print("\n=== H2假设检验：Type 4高度失效的多因素根因分析 ===")
        
        # 多因素ANOVA分析
        # 选择影响高度的关键参数
        factors = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                  's1_hold', 's2_hold', 'electrode_pressure']
        
        # 准备数据
        anova_data = self.processed_data[['post_weld_disk_holder_height', 'is_type_4'] + factors].dropna()
        
        # 单因素ANOVA
        anova_results = {}
        for factor in factors:
            if factor in anova_data.columns:
                groups = []
                for value in anova_data[factor].unique():
                    group_data = anova_data[anova_data[factor] == value]['post_weld_disk_holder_height']
                    if len(group_data) > 1:
                        groups.append(group_data)
                
                if len(groups) >= 2:
                    f_stat, p_val = stats.f_oneway(*groups)
                    anova_results[factor] = {'F_statistic': f_stat, 'p_value': p_val}
        
        print("单因素ANOVA结果:")
        for factor, result in anova_results.items():
            print(f"{factor}: F={result['F_statistic']:.4f}, p={result['p_value']:.4f}")
        
        # 多元线性回归
        X = anova_data[factors].copy()
        y = anova_data['post_weld_disk_holder_height']
        
        # 确保数据类型正确
        X = X.astype(float)
        y = y.astype(float)
        
        X = sm.add_constant(X)
        linear_model = sm.OLS(y, X).fit()
        
        print("\n多元线性回归结果:")
        print(linear_model.summary())
        
        # 决策树分析
        X_tree = anova_data[factors]
        y_tree = anova_data['is_type_4']
        
        dt = DecisionTreeClassifier(max_depth=3, random_state=42)
        dt.fit(X_tree, y_tree)
        
        print(f"\n决策树准确率: {dt.score(X_tree, y_tree):.4f}")
        print("特征重要性:")
        for i, feature in enumerate(factors):
            print(f"{feature}: {dt.feature_importances_[i]:.4f}")
        
        # 保存结果
        self.results['h2_testing'] = {
            'anova_results': anova_results,
            'linear_regression': linear_model,
            'decision_tree': dt,
            'feature_importance': dict(zip(factors, dt.feature_importances_))
        }
        
        return self.results['h2_testing']
    
    def optimal_parameter_identification(self):
        """最优参数识别"""
        print("\n=== 最优参数识别 ===")
        
        # 分析不同参数组合的成功率
        success_rates = {}
        
        # 按电流参数分组
        current_groups = self.processed_data.groupby(['s1_percent_current', 's2_percent_current'])
        
        for (s1_current, s2_current), group in current_groups:
            if len(group) >= 3:  # 至少3个样本
                success_rate = group['is_pass'].mean()
                sample_size = len(group)
                
                # 计算置信区间
                if success_rate > 0 and success_rate < 1:
                    se = np.sqrt(success_rate * (1 - success_rate) / sample_size)
                    ci_lower = success_rate - 1.96 * se
                    ci_upper = success_rate + 1.96 * se
                else:
                    ci_lower = ci_upper = success_rate
                
                success_rates[(s1_current, s2_current)] = {
                    'success_rate': success_rate,
                    'sample_size': sample_size,
                    'ci_lower': ci_lower,
                    'ci_upper': ci_upper
                }
        
        # 按成功率排序
        sorted_params = sorted(success_rates.items(), key=lambda x: x[1]['success_rate'], reverse=True)
        
        print("参数组合成功率排名:")
        for i, (params, stats) in enumerate(sorted_params[:10]):
            print(f"{i+1}. S1={params[0]}%, S2={params[1]}%: "
                  f"成功率={stats['success_rate']:.2%} (n={stats['sample_size']}, "
                  f"95% CI: [{stats['ci_lower']:.2%}, {stats['ci_upper']:.2%}])")
        
        # 两因素ANOVA分析交互作用
        formula = 'is_pass ~ C(s1_percent_current) + C(s2_percent_current) + C(s1_percent_current):C(s2_percent_current)'
        
        # 创建用于ANOVA的数据框
        anova_df = self.processed_data[['is_pass', 's1_percent_current', 's2_percent_current']].dropna()
        
        try:
            model = ols(formula, data=anova_df).fit()
            anova_table = anova_lm(model)
            print("\n两因素ANOVA结果:")
            print(anova_table)
        except Exception as e:
            print(f"两因素ANOVA分析失败: {e}")
        
        # 保存结果
        self.results['optimal_parameters'] = {
            'success_rates': success_rates,
            'sorted_params': sorted_params,
            'anova_table': anova_table if 'anova_table' in locals() else None
        }
        
        return self.results['optimal_parameters']
    
    def predictive_modeling(self):
        """预测模型开发"""
        print("\n=== 预测模型开发 ===")
        
        # 准备特征和目标变量
        feature_cols = ['s1_percent_current', 's2_percent_current', 's1_weld_heat', 's2_weld_heat',
                       's1_hold', 's2_hold', 'electrode_pressure', 'electrode_height']
        
        X = self.processed_data[feature_cols].fillna(0).astype(float)
        y = self.processed_data['is_pass'].astype(float)
        
        # 分割训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        models = {
            'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
            'SVM': SVC(kernel='rbf', probability=True, random_state=42),
            'XGBoost': xgb.XGBClassifier(random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42)
        }
        
        model_results = {}
        
        for name, model in models.items():
            print(f"\n训练 {name} 模型...")
            
            # 训练模型
            model.fit(X_train, y_train)
            
            # 预测
            y_pred = model.predict(X_test)
            y_prob = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
            
            # 评估指标
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            # 交叉验证
            cv_scores = cross_val_score(model, X, y, cv=5, scoring='accuracy')
            
            # AUC-ROC
            auc_roc = roc_auc_score(y_test, y_prob) if y_prob is not None else None
            
            model_results[name] = {
                'model': model,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'auc_roc': auc_roc
            }
            
            print(f"准确率: {accuracy:.4f}")
            print(f"精确率: {precision:.4f}")
            print(f"召回率: {recall:.4f}")
            print(f"F1分数: {f1:.4f}")
            print(f"交叉验证准确率: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            if auc_roc:
                print(f"AUC-ROC: {auc_roc:.4f}")
        
        # 找出最佳模型
        best_model_name = max(model_results.keys(), key=lambda x: model_results[x]['accuracy'])
        best_model = model_results[best_model_name]['model']
        
        print(f"\n最佳模型: {best_model_name}")
        
        # SHAP值分析
        if hasattr(best_model, 'predict_proba'):
            print("\nSHAP值分析...")
            try:
                explainer = shap.TreeExplainer(best_model)
                shap_values = explainer.shap_values(X_test)
                
                # 特征重要性
                feature_importance = np.abs(shap_values).mean(axis=0)
                importance_df = pd.DataFrame({
                    'feature': feature_cols,
                    'importance': feature_importance
                }).sort_values('importance', ascending=False)
                
                print("特征重要性 (SHAP值):")
                print(importance_df)
                
                model_results['shap_importance'] = importance_df
                
            except Exception as e:
                print(f"SHAP分析失败: {e}")
        
        # 保存结果
        self.results['predictive_models'] = model_results
        self.results['best_model'] = best_model_name
        
        return self.results['predictive_models']
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        print("\n=== 生成综合分析报告 ===")
        
        report = {
            'analysis_date': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
            'sample_size': len(self.processed_data),
            'key_findings': {},
            'recommendations': {}
        }
        
        # H1假设检验结论
        if 'h1_testing' in self.results:
            h1_result = self.results['h1_testing']
            report['key_findings']['h1_hypothesis'] = {
                'hypothesis': "99%电流与Type 8裂纹失效相关",
                'chi2_p_value': h1_result['p_value'],
                'is_significant': h1_result['p_value'] < 0.05,
                'effect_size': h1_result['cramers_v'],
                'odds_ratio': h1_result['odds_ratios'].get('is_99_percent_any', None)
            }
        
        # H2假设检验结论
        if 'h2_testing' in self.results:
            h2_result = self.results['h2_testing']
            significant_factors = {
                factor: stats['p_value'] < 0.05 
                for factor, stats in h2_result['anova_results'].items()
            }
            
            report['key_findings']['h2_hypothesis'] = {
                'hypothesis': "Type 4高度失效的多因素根因分析",
                'significant_factors': significant_factors,
                'model_r2': h2_result['linear_regression'].rsquared
            }
        
        # 最优参数推荐
        if 'optimal_parameters' in self.results:
            optimal_params = self.results['optimal_parameters']['sorted_params']
            if optimal_params:
                best_params = optimal_params[0]
                report['recommendations']['optimal_parameters'] = {
                    's1_current': best_params[0][0],
                    's2_current': best_params[0][1],
                    'success_rate': best_params[1]['success_rate'],
                    'confidence_interval': [best_params[1]['ci_lower'], best_params[1]['ci_upper']]
                }
        
        # 预测模型性能
        if 'predictive_models' in self.results:
            best_model = self.results['best_model']
            model_stats = self.results['predictive_models'][best_model]
            
            report['key_findings']['predictive_model'] = {
                'best_model': best_model,
                'accuracy': model_stats['accuracy'],
                'cv_accuracy': model_stats['cv_mean'],
                'auc_roc': model_stats['auc_roc']
            }
        
        # 打印报告摘要
        print("\n=== 分析报告摘要 ===")
        print(f"分析日期: {report['analysis_date']}")
        print(f"样本数量: {report['sample_size']}")
        
        if 'h1_hypothesis' in report['key_findings']:
            h1 = report['key_findings']['h1_hypothesis']
            print(f"\nH1假设检验结果:")
            print(f"假设: {h1['hypothesis']}")
            print(f"显著性: {'是' if h1['is_significant'] else '否'} (p={h1['chi2_p_value']:.4f})")
            print(f"效应量: {h1['effect_size']:.4f}")
        
        if 'h2_hypothesis' in report['key_findings']:
            h2 = report['key_findings']['h2_hypothesis']
            print(f"\nH2假设检验结果:")
            print(f"假设: {h2['hypothesis']}")
            print(f"显著因素: {h2['significant_factors']}")
            print(f"模型R²: {h2['model_r2']:.4f}")
        
        if 'optimal_parameters' in report['recommendations']:
            opt = report['recommendations']['optimal_parameters']
            print(f"\n最优参数推荐:")
            print(f"S1电流: {opt['s1_current']}%, S2电流: {opt['s2_current']}%")
            print(f"预期成功率: {opt['success_rate']:.2%}")
            print(f"95%置信区间: [{opt['confidence_interval'][0]:.2%}, {opt['confidence_interval'][1]:.2%}]")
        
        if 'predictive_model' in report['key_findings']:
            pm = report['key_findings']['predictive_model']
            print(f"\n预测模型性能:")
            print(f"最佳模型: {pm['best_model']}")
            print(f"准确率: {pm['accuracy']:.4f}")
            print(f"交叉验证准确率: {pm['cv_accuracy']:.4f}")
            if pm['auc_roc']:
                print(f"AUC-ROC: {pm['auc_roc']:.4f}")
        
        self.results['comprehensive_report'] = report
        return report
    
    def run_complete_analysis(self):
        """运行完整的统计分析流程"""
        print("开始G3P OP60电阻焊接工艺统计分析...")
        
        # Phase 1: 数据准备
        self.load_data()
        self.extract_double_s1s2_samples()
        self.handle_missing_values()
        self.create_binary_variables()
        self.standardize_variables()
        
        # Phase 2: 描述性统计
        self.descriptive_statistics()
        self.correlation_analysis()
        
        # Phase 3: H1假设检验
        self.h1_hypothesis_testing()
        
        # Phase 4: H2假设检验
        self.h2_hypothesis_testing()
        
        # Phase 5: 最优参数识别
        self.optimal_parameter_identification()
        
        # Phase 6: 预测模型
        self.predictive_modeling()
        
        # Phase 7: 综合报告
        self.generate_comprehensive_report()
        
        print("\n统计分析完成!")
        return self.results

# 主程序
if __name__ == "__main__":
    # 创建分析实例
    data_path = '/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/NEL_G3P_Disk_Holder_Welding_Optimization_OP60.csv'
    
    analyzer = G3PStatisticalAnalysis(data_path)
    
    # 运行完整分析
    results = analyzer.run_complete_analysis()
    
    print("\n分析完成！结果已保存在analyzer.results中")
    
    # 保存结果到文件
    import pickle
    with open('/Users/<USER>/Library/Mobile Documents/com~apple~CloudDocs/Welding/G3P_OP60_GasFill_Resistance_Weld_DOE/statistical_analysis_results.pkl', 'wb') as f:
        pickle.dump(results, f)
    
    print("分析结果已保存到 statistical_analysis_results.pkl")