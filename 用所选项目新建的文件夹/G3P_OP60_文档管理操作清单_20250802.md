# G3P OP60 文档管理操作清单

**执行日期**: 2025年8月2日  
**目的**: 提供具体的文件操作指令，实现项目文档的系统性整理  
**基于**: 项目文档管理计划完整版

---

## 📋 操作总览

| 操作类型 | 文件数量 | 操作说明 |
|----------|----------|----------|
| **🟢 保留** | 25个 | 直接保留，重新组织结构 |
| **🟡 标注保留** | 25个 | 添加过时声明后保留 |
| **🔴 删除/归档** | 31个 | 删除或移动到归档文件夹 |
| **📁 新建** | 8个 | 创建新的文档管理文件 |
| **总计** | **89个** | 完整的文档管理操作 |

---

## 🟢 第一步：核心文档保留和重组 (25个文件)

### A. 最终技术分析文档 (7个)

#### 操作指令
```bash
# 创建核心技术文档文件夹
mkdir "01_Core_Technical_Documents"
mkdir "01_Core_Technical_Documents/Final_Analysis"

# 移动并重命名最终技术文档
move "DOE_v7.1_生产约束下的根因重新分析_20250802.md" "01_Core_Technical_Documents/Final_Analysis/01_FINAL_根因分析_v1.0_20250802.md"
move "DOE_v7.1_H1重新设计执行指令_基于生产约束_20250802.md" "01_Core_Technical_Documents/Final_Analysis/02_EXEC_H1执行指令_v1.0_20250802.md"
move "DOE_v7.1_H1验证统计分析审核报告_20250802.md" "01_Core_Technical_Documents/Final_Analysis/03_FINAL_统计验证_v1.0_20250802.md"
move "DOE_v7.1_设计与实际结果偏差分析报告_20250802.md" "01_Core_Technical_Documents/Final_Analysis/04_FINAL_偏差分析_v1.0_20250802.md"
move "DOE_v7.1_阶段性总结分析_执行报告_20250802.md" "01_Core_Technical_Documents/Final_Analysis/05_FINAL_执行总结_v1.0_20250802.md"
move "DOE_v7.1_文档管理和一致性审查报告_20250802.md" "01_Core_Technical_Documents/Final_Analysis/06_FINAL_一致性审查_v1.0_20250802.md"
move "DOE_v7.1_文档管理执行清单_20250802.md" "01_Core_Technical_Documents/Final_Analysis/07_FINAL_管理清单_v1.0_20250802.md"
```

### B. 数据和分析脚本 (7个)

#### 操作指令
```bash
# 创建数据分析文件夹
mkdir "02_Data_And_Analysis"
mkdir "02_Data_And_Analysis/Raw_Data"
mkdir "02_Data_And_Analysis/Analysis_Scripts"
mkdir "02_Data_And_Analysis/Results"

# 移动核心数据文件
move "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" "02_Data_And_Analysis/Raw_Data/DATA_实验数据_v7.1_20250802.csv"
move "失效模式定义.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义_v1.0.xlsx"
move "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_参数定义_v1.0.xlsx"
move "statistical_analysis_results.pkl" "02_Data_And_Analysis/Results/DATA_分析结果_v1.0.pkl"

# 移动核心分析脚本
move "H1_Data_Verification.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_H1数据验证_v1.0.py"
move "H1_Analysis_Manual.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_H1手动分析_v1.0.py"
move "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE分析_v1.0.py"
```

### C. 正式报告和参考文档 (5个)

#### 操作指令
```bash
# 创建正式报告文件夹
mkdir "03_Official_Reports"
mkdir "03_Official_Reports/Equipment_Documentation"

# 移动正式报告
move "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf" "03_Official_Reports/REPORT_阶段总结报告_v1.0.pdf"
move "Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf" "03_Official_Reports/REPORT_优化参数报告_v1.0.pdf"
move "充气机电阻焊接说明书 - 英文版.pdf" "03_Official_Reports/Equipment_Documentation/REF_设备说明书_v1.0.pdf"
move "Resistance Welding Process.pdf" "03_Official_Reports/Equipment_Documentation/REF_工艺说明_v1.0.pdf"
```

### D. 数据可视化文件 (6个)

#### 操作指令
```bash
# 创建可视化文件夹
mkdir "02_Data_And_Analysis/Results/Visualizations"

# 移动可视化图表
move "g3p_data_exploration.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_数据探索_v1.0.png"
move "g3p_data_exploration_v2.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_数据探索_v2.0.png"
move "g3p_h1_hypothesis_verification.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_H1假设验证_v1.0.png"
move "g3p_h2_hypothesis_verification.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_H2假设验证_v1.0.png"
move "g3p_parameter_optimization.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_参数优化_v1.0.png"
move "g3p_prediction_models_v2.png" "02_Data_And_Analysis/Results/Visualizations/VIZ_预测模型_v2.0.png"
```

---

## 🟡 第二步：有条件保留文档处理 (25个文件)

### A. 需要标注的技术文档 (8个)

#### 操作指令
```bash
# 创建参考文档文件夹
mkdir "04_Reference_Documents"
mkdir "04_Reference_Documents/Historical_Analysis"
mkdir "04_Reference_Documents/Process_Documentation"

# 移动并标注历史分析文档
move "DOE_v7.1_H1阶段5M1E根因分析报告_20250802.md" "04_Reference_Documents/Historical_Analysis/REF_5M1E根因分析_v1.0_20250802.md"
move "DOE_v7.1_反向验证和根因分析执行总结_20250802.md" "04_Reference_Documents/Historical_Analysis/REF_反向验证总结_v1.0_20250802.md"
move "G3P_OP60_DOE执行指令_V7.1_Word版本.md" "04_Reference_Documents/Historical_Analysis/REF_DOE执行指令_v7.1.md"
move "DOE_v7.1_第一阶段H1假设验证统计分析报告.md" "04_Reference_Documents/Historical_Analysis/REF_H1统计分析_v1.0.md"
```

#### 标注内容添加
为每个移动的文档添加以下标注：
```markdown
⚠️ 重要声明: 本文档包含部分过时内容
- 创建日期: [原始日期]
- 过时原因: [具体原因]
- 最新版本: 请参考 01_Core_Technical_Documents/Final_Analysis/ 中的最终版本
- 参考价值: [说明仍有价值的部分]
```

### B. 参考价值脚本 (6个)

#### 操作指令
```bash
# 创建参考脚本文件夹
mkdir "02_Data_And_Analysis/Reference_Scripts"

# 移动参考脚本
move "G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py" "02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py"
move "g3p_statistical_analysis.py" "02_Data_And_Analysis/Reference_Scripts/REF_统计分析_v1.0.py"
move "g3p_comprehensive_statistical_analysis.py" "02_Data_And_Analysis/Reference_Scripts/REF_综合分析_v1.0.py"
move "current_status_analysis.py" "02_Data_And_Analysis/Reference_Scripts/REF_状态分析_v1.0.py"
move "check_data_structure.py" "02_Data_And_Analysis/Reference_Scripts/REF_数据结构检查_v1.0.py"
move "analysis_summary.py" "02_Data_And_Analysis/Reference_Scripts/REF_分析总结_v1.0.py"
```

### C. 其他参考文档 (11个)

#### 操作指令
```bash
# 移动其他参考文档
move "G3P OP60工位破裂盘电阻焊接工艺优化 - 电阻焊接设备优化总结.pdf" "04_Reference_Documents/Process_Documentation/REF_设备优化总结_v1.0.pdf"
move "G3P OP60工位破裂盘电阻焊接工艺解析.pdf" "04_Reference_Documents/Process_Documentation/REF_工艺解析_v1.0.pdf"
move "G3P DOE文档解读.docx" "04_Reference_Documents/Process_Documentation/REF_DOE文档解读_v1.0.docx"
move "double_s1s2 优化阶段参数矩阵表.docx" "04_Reference_Documents/Process_Documentation/REF_参数矩阵表_v1.0.docx"

# 移动现场照片
mkdir "04_Reference_Documents/Site_Photos"
move "生产线OP60图片" "04_Reference_Documents/Site_Photos/现场照片_OP60生产线"
```

---

## 🔴 第三步：删除/归档文件处理 (31个文件)

### A. 创建归档文件夹

#### 操作指令
```bash
# 创建归档文件夹
mkdir "05_Archive"
mkdir "05_Archive/Deprecated_Files"
mkdir "05_Archive/Deprecated_Files/Early_Analysis"
mkdir "05_Archive/Deprecated_Files/Duplicate_Scripts"
mkdir "05_Archive/Deprecated_Files/Unrelated_Files"
```

### B. 过时技术分析文档 (17个)

#### 操作指令
```bash
# 移动过时分析文档到归档
move "DOE_v7_1_Stage1_重新分析报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_早期重新分析_v1.0.md"
move "G3P_OP60_专业技术报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_专业技术报告_v1.0.md"
move "G3P_OP60_专业技术报告.html" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_专业技术报告_v1.0.html"
move "G3P_OP60_修正报告方法论完整性评估.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_方法论评估_v1.0.md"
move "G3P_OP60_关键修正任务完成报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_修正任务报告_v1.0.md"
move "G3P_OP60_审核后完整技术分析总结报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_技术分析总结_v1.0.md"
move "G3P_OP60_技术报告交叉审核报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_交叉审核报告_v1.0.md"
move "G3P_OP60_方法论完整性评估总结.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_方法论评估总结_v1.0.md"
move "G3P_OP60_统计分析修正报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_统计分析修正_v1.0.md"
move "G3P_OP60_统计分析完整报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_统计分析完整_v1.0.md"
move "G3P_OP60_统计分析计划.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_统计分析计划_v1.0.md"
move "G3P_OP60_项目历史审核与下一步计划.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_项目历史审核_v1.0.md"
move "G3P_OP60_项目执行检查清单.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_项目执行清单_v1.0.md"
move "G3P_OP60项目文件结构总结报告.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_文件结构总结_v1.0.md"
move "G3P电阻焊接项目文件结构分析__2025-08-01T13-30-14.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_文件结构分析_v1.0.md"
move "G3P_OP60_Resistance_Welding_Optimization_Analysis_Report.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_优化分析报告_v1.0.md"
move "G3P_OP60_DOE_v7_1_反向验证审核报告_2025-01-27.md" "05_Archive/Deprecated_Files/Early_Analysis/ARCH_反向验证审核_v1.0.md"
```

### C. 重复脚本文件 (9个)

#### 操作指令
```bash
# 移动重复脚本到归档
move "G3P_OP60_第一阶段数据分析" "05_Archive/Deprecated_Files/Duplicate_Scripts/第一阶段数据分析_重复脚本"
move "g3p_analysis_simple.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_简单分析_v1.0.py"
move "g3p_failure_analysis.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_失效分析_v1.0.py"
move "g3p_final_analysis.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_最终分析_v1.0.py"
move "g3p_pdf_report_generator.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_PDF报告生成_v1.0.py"
move "g3p_resistance_welding_analysis.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_电阻焊分析_v1.0.py"
move "generate_pdf_report.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_PDF报告_v1.0.py"
move "extract_pdf.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_PDF提取_v1.0.py"
move "pdf_extractor.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_PDF提取器_v1.0.py"
move "test_model.py" "05_Archive/Deprecated_Files/Duplicate_Scripts/ARCH_模型测试_v1.0.py"
```

### D. 无关文件 (5个)

#### 操作指令
```bash
# 移动无关文件到归档
move "B0742300.pdf" "05_Archive/Deprecated_Files/Unrelated_Files/ARCH_无关文档1.pdf"
move "B0984900.pdf" "05_Archive/Deprecated_Files/Unrelated_Files/ARCH_无关文档2.pdf"
move "G2P OP58焊接深度位移量 copy.xlsx" "05_Archive/Deprecated_Files/Unrelated_Files/ARCH_其他项目数据.xlsx"
move "Resistance Welding Process.docx" "05_Archive/Deprecated_Files/Unrelated_Files/ARCH_重复工艺文档.docx"
move "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pptx" "05_Archive/Deprecated_Files/Unrelated_Files/ARCH_演示文稿版本.pptx"

# 删除快捷方式文件
del "LL2024-0184 G3P OP60 Disk Holders Weld - Shortcut.lnk"
del "NS24-0423  SI2024-0149 - Shortcut.lnk"

# 清理缓存文件夹
rmdir /s "__pycache__"
```

---

## 📁 第四步：新建文档管理文件 (8个)

### A. 项目管理文档

#### 操作指令
```bash
# 创建项目管理文件夹
mkdir "01_Core_Technical_Documents/Project_Management"
mkdir "01_Core_Technical_Documents/Knowledge_Base"

# 移动新建的管理文档
move "G3P_OP60_项目文档管理计划_完整版_20250802.md" "01_Core_Technical_Documents/Project_Management/MGMT_文档管理计划_v1.0_20250802.md"
move "G3P_OP60_项目知识库总结_20250802.md" "01_Core_Technical_Documents/Knowledge_Base/KB_项目知识库总结_v1.0_20250802.md"
move "G3P_OP60_文档管理操作清单_20250802.md" "01_Core_Technical_Documents/Project_Management/MGMT_操作清单_v1.0_20250802.md"
```

### B. 创建索引和说明文件

#### 文件1: 项目根目录README
```bash
# 创建项目说明文件
echo "# G3P OP60 电阻焊DOE项目档案

## 项目概述
G3P OP60工位破裂盘气体填充电阻焊工艺优化项目的完整技术档案

## 文件夹结构
- 01_Core_Technical_Documents/ - 核心技术文档
- 02_Data_And_Analysis/ - 数据和分析文件
- 03_Official_Reports/ - 正式报告
- 04_Reference_Documents/ - 参考文档
- 05_Archive/ - 归档文件

## 最后更新
2025年8月2日

## 联系信息
技术负责人: [姓名]
项目经理: [姓名]" > README.md
```

#### 文件2: 文档索引
```bash
# 创建文档索引文件
echo "# 文档索引

## 核心技术文档
1. 01_FINAL_根因分析_v1.0_20250802.md - 最终根因分析
2. 02_EXEC_H1执行指令_v1.0_20250802.md - H1重新设计执行指令
3. 03_FINAL_统计验证_v1.0_20250802.md - 统计分析验证
4. 04_FINAL_偏差分析_v1.0_20250802.md - 设计偏差分析
5. 05_FINAL_执行总结_v1.0_20250802.md - 阶段性总结

## 核心数据文件
- DATA_实验数据_v7.1_20250802.csv - 主要实验数据
- SCRIPT_H1数据验证_v1.0.py - 数据验证脚本

## 使用说明
请优先参考核心技术文档，其他文档仅作参考。" > "文档索引.md"
```

---

## ✅ 第五步：质量检查清单

### A. 文件结构检查
- [ ] 所有核心文档已正确分类
- [ ] 文件命名符合标准规范
- [ ] 文件夹结构清晰合理
- [ ] 过时文档已添加标注

### B. 内容完整性检查
- [ ] 核心技术结论完整保留
- [ ] 重要数据文件完整保留
- [ ] 关键分析脚本完整保留
- [ ] 正式报告完整保留

### C. 可用性检查
- [ ] 文档索引准确完整
- [ ] 文件路径引用正确
- [ ] 标注说明清晰明确
- [ ] 使用说明详细易懂

---

**操作负责人**: [技术负责人]  
**完成期限**: 2025年8月2日  
**质量检查**: [质量工程师]  
**最终确认**: [项目经理]

---

*本操作清单提供了完整的文档管理实施步骤，确保项目知识的有效保留和高效管理。*
