#!/usr/bin/env python3
"""
G3P OP60 Gas Fill Resistance Welding - Current Status Analysis
分析当前G3P充气工位电阻焊接的实际情况
"""

import csv
import os
from collections import defaultdict, Counter

def load_csv_data(filename):
    """加载CSV数据文件"""
    data = []
    if not os.path.exists(filename):
        print(f"文件不存在: {filename}")
        return data
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                data.append(row)
        print(f"成功加载 {len(data)} 条记录从 {filename}")
    except Exception as e:
        print(f"加载文件错误 {filename}: {e}")
    
    return data

def analyze_failure_modes(data):
    """分析失效模式"""
    print("\n=== 失效模式分析 ===")
    
    failure_types = Counter()
    leakage_status = Counter()
    
    for row in data:
        failure_type = row.get('weld_failure_type', 'Unknown')
        leakage = row.get('leakage', 'Unknown')
        
        failure_types[failure_type] += 1
        leakage_status[leakage] += 1
    
    print("失效类型分布:")
    for failure_type, count in failure_types.most_common():
        percentage = (count / len(data)) * 100
        print(f"  类型 {failure_type}: {count} 次 ({percentage:.1f}%)")
    
    print("\n泄漏测试结果:")
    for status, count in leakage_status.most_common():
        percentage = (count / len(data)) * 100
        print(f"  {status}: {count} 次 ({percentage:.1f}%)")

def analyze_welding_parameters(data):
    """分析焊接参数"""
    print("\n=== 焊接参数分析 ===")
    
    # 分析主要参数的分布
    s1_current = []
    s1_hold = []
    s2_current = []
    s2_hold = []
    electrode_pressure = []
    
    for row in data:
        try:
            s1_current.append(float(row.get('s1_percent_current', 0)))
            s1_hold.append(float(row.get('s1_hold', 0)))
            s2_current.append(float(row.get('s2_percent_current', 0)))
            s2_hold.append(float(row.get('s2_hold', 0)))
            electrode_pressure.append(float(row.get('electrode_pressure', 0)))
        except ValueError:
            continue
    
    def print_parameter_stats(param_list, param_name):
        if param_list:
            print(f"\n{param_name}:")
            print(f"  范围: {min(param_list)} - {max(param_list)}")
            print(f"  平均值: {sum(param_list)/len(param_list):.1f}")
            print(f"  最常用值: {Counter(param_list).most_common(3)}")
    
    print_parameter_stats(s1_current, "S1电流百分比")
    print_parameter_stats(s1_hold, "S1保持时间")
    print_parameter_stats(s2_current, "S2电流百分比")
    print_parameter_stats(s2_hold, "S2保持时间")
    print_parameter_stats(electrode_pressure, "电极压力")

def analyze_quality_metrics(data):
    """分析质量指标"""
    print("\n=== 质量指标分析 ===")
    
    heights = []
    weld_widths_a = []
    weld_widths_b = []
    
    for row in data:
        try:
            height = float(row.get('post_weld_disk_holder_height', 0))
            if height > 0:
                heights.append(height)
            
            width_a = float(row.get('weld_width_a', 0))
            if width_a > 0:
                weld_widths_a.append(width_a)
            
            width_b = float(row.get('weld_width_b', 0))
            if width_b > 0:
                weld_widths_b.append(width_b)
        except ValueError:
            continue
    
    def print_quality_stats(values, metric_name, target_range=None):
        if values:
            print(f"\n{metric_name}:")
            print(f"  样本数: {len(values)}")
            print(f"  范围: {min(values):.3f} - {max(values):.3f}")
            print(f"  平均值: {sum(values)/len(values):.3f}")
            if target_range:
                in_range = sum(1 for v in values if target_range[0] <= v <= target_range[1])
                print(f"  目标范围 {target_range}: {in_range}/{len(values)} ({in_range/len(values)*100:.1f}%)")
    
    print_quality_stats(heights, "焊接后高度 (mm)", (2.1, 2.4))
    print_quality_stats(weld_widths_a, "焊缝宽度A (mm)", (1.1, 1.3))
    print_quality_stats(weld_widths_b, "焊缝宽度B (mm)", (1.1, 1.3))

def analyze_schedule_types(data):
    """分析焊接日程类型"""
    print("\n=== 焊接日程类型分析 ===")
    
    schedule_types = Counter()
    schedule_success = defaultdict(list)
    
    for row in data:
        schedule = row.get('schedule_type', 'Unknown')
        failure_type = row.get('weld_failure_type', 'Unknown')
        
        schedule_types[schedule] += 1
        schedule_success[schedule].append(failure_type == '1')  # 假设'1'表示成功
    
    print("日程类型分布:")
    for schedule, count in schedule_types.most_common():
        if schedule in schedule_success:
            success_rate = sum(schedule_success[schedule]) / len(schedule_success[schedule]) * 100
            print(f"  {schedule}: {count} 次, 成功率: {success_rate:.1f}%")
        else:
            print(f"  {schedule}: {count} 次")

def main():
    """主函数"""
    print("G3P OP60 充气工位电阻焊接 - 当前状况分析")
    print("=" * 50)
    
    # 数据文件路径
    data_files = [
        'NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv',
        'data_2025-01-18_03-46-49.csv',
        '2025-01-04_19-37-52.csv'
    ]
    
    all_data = []
    
    # 加载所有数据文件
    for filename in data_files:
        data = load_csv_data(filename)
        all_data.extend(data)
    
    if not all_data:
        print("没有找到有效的数据文件!")
        return
    
    print(f"\n总共分析 {len(all_data)} 条记录")
    
    # 执行各项分析
    analyze_failure_modes(all_data)
    analyze_welding_parameters(all_data)
    analyze_quality_metrics(all_data)
    analyze_schedule_types(all_data)
    
    print("\n=== 分析完成 ===")
    print("基于以上分析结果，我们可以制定下一步的优化计划。")

if __name__ == "__main__":
    main()
