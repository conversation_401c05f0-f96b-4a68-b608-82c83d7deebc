# G3P OP60 自动化文件移动脚本
# PowerShell Script for File Reorganization
# 执行前请确保已备份重要文件

Write-Host "开始G3P OP60项目文件重组..." -ForegroundColor Green

# 设置工作目录
$ProjectRoot = Get-Location

# 核心数据文件移动
Write-Host "移动核心数据文件..." -ForegroundColor Yellow

# 移动实验数据
if (Test-Path "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv") {
    Move-Item "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" "02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv"
    Write-Host "✅ 实验数据文件已移动" -ForegroundColor Green
}

# 移动参数定义文件
if (Test-Path "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx") {
    Move-Item "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx"
    Write-Host "✅ 参数定义文件已移动" -ForegroundColor Green
}

# 移动失效模式定义
if (Test-Path "失效模式定义.xlsx") {
    Move-Item "失效模式定义.xlsx" "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx"
    Write-Host "✅ 失效模式定义已移动" -ForegroundColor Green
}

# 分析脚本移动
Write-Host "移动分析脚本..." -ForegroundColor Yellow

# 核心分析脚本
if (Test-Path "G3P_OP60_DOE_V7.1_Stage1_Analysis.py") {
    Move-Item "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py"
    Write-Host "✅ 核心分析脚本已移动" -ForegroundColor Green
}

# 参考脚本
$ReferenceScripts = @(
    @{Source="G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py"; Target="02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py"},
    @{Source="H1_Analysis_Manual.py"; Target="02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py"},
    @{Source="H1_Data_Verification.py"; Target="02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py"}
)

foreach ($script in $ReferenceScripts) {
    if (Test-Path $script.Source) {
        Move-Item $script.Source $script.Target
        Write-Host "✅ 参考脚本 $($script.Source) 已移动" -ForegroundColor Green
    }
}

# 可视化结果移动
Write-Host "移动可视化结果..." -ForegroundColor Yellow

$VisualizationFiles = @(
    @{Source="g3p_h1_hypothesis_verification.png"; Target="02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证图表.png"},
    @{Source="g3p_h2_hypothesis_verification.png"; Target="02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证图表.png"},
    @{Source="g3p_parameter_optimization.png"; Target="02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化图表.png"}
)

foreach ($viz in $VisualizationFiles) {
    if (Test-Path $viz.Source) {
        Move-Item $viz.Source $viz.Target
        Write-Host "✅ 可视化文件 $($viz.Source) 已移动" -ForegroundColor Green
    }
}

# 正式报告移动
Write-Host "移动正式报告..." -ForegroundColor Yellow

$ReportFiles = @(
    @{Source="NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf"; Target="03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf"},
    @{Source="Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf"; Target="03_Official_Reports/REPORT_优化参数_G3P_OP60_20250731.pdf"}
)

foreach ($report in $ReportFiles) {
    if (Test-Path $report.Source) {
        Move-Item $report.Source $report.Target
        Write-Host "✅ 报告文件 $($report.Source) 已移动" -ForegroundColor Green
    }
}

# 设备文档移动
$EquipmentDocs = @(
    @{Source="充气机电阻焊接说明书 - 英文版.pdf"; Target="03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书.pdf"},
    @{Source="B0742300.pdf"; Target="03_Official_Reports/Equipment_Documentation/EQUIP_B0742300.pdf"},
    @{Source="B0984900.pdf"; Target="03_Official_Reports/Equipment_Documentation/EQUIP_B0984900.pdf"}
)

foreach ($equip in $EquipmentDocs) {
    if (Test-Path $equip.Source) {
        Move-Item $equip.Source $equip.Target
        Write-Host "✅ 设备文档 $($equip.Source) 已移动" -ForegroundColor Green
    }
}

# 现场照片移动
if (Test-Path "生产线OP60图片") {
    Move-Item "生产线OP60图片" "04_Reference_Documents/Site_Photos/生产线OP60现场照片"
    Write-Host "✅ 现场照片文件夹已移动" -ForegroundColor Green
}

# 工艺文档移动
$ProcessDocs = @(
    @{Source="Resistance Welding Process.pdf"; Target="04_Reference_Documents/Process_Documentation/PROC_电阻焊接工艺.pdf"},
    @{Source="double_s1s2 优化阶段参数矩阵表.docx"; Target="04_Reference_Documents/Process_Documentation/PROC_参数矩阵表.docx"}
)

foreach ($proc in $ProcessDocs) {
    if (Test-Path $proc.Source) {
        Move-Item $proc.Source $proc.Target
        Write-Host "✅ 工艺文档 $($proc.Source) 已移动" -ForegroundColor Green
    }
}

# 归档文件移动
Write-Host "移动归档文件..." -ForegroundColor Yellow

# 早期分析文档
$EarlyAnalysisDocs = @(
    "DOE_v7.1_H1阶段5M1E根因分析报告_20250802.md",
    "DOE_v7.1_反向验证和根因分析执行总结_20250802.md",
    "DOE_v7.1_第一阶段H1假设验证统计分析报告.md",
    "G3P_OP60_DOE执行指令_V7.1_Word版本.md"
)

foreach ($doc in $EarlyAnalysisDocs) {
    if (Test-Path $doc) {
        Move-Item $doc "05_Archive/Deprecated_Files/Early_Analysis/"
        Write-Host "✅ 早期分析文档 $doc 已归档" -ForegroundColor Green
    }
}

# 重复脚本文件夹
if (Test-Path "G3P_OP60_第一阶段数据分析") {
    Move-Item "G3P_OP60_第一阶段数据分析" "05_Archive/Deprecated_Files/Duplicate_Scripts/"
    Write-Host "✅ 重复脚本文件夹已归档" -ForegroundColor Green
}

if (Test-Path "__pycache__") {
    Move-Item "__pycache__" "05_Archive/Deprecated_Files/Duplicate_Scripts/"
    Write-Host "✅ Python缓存文件夹已归档" -ForegroundColor Green
}

Write-Host "文件重组完成！" -ForegroundColor Green
Write-Host "请检查各文件夹确认文件已正确移动。" -ForegroundColor Yellow
