# G3P OP60项目 - 清理和验证脚本
# 日期: 2025-08-03
# 目的: 清理重复文件并验证重组结果

Write-Host "=== G3P OP60项目清理和验证脚本 ===" -ForegroundColor Green
Write-Host "日期: 2025-08-03" -ForegroundColor Yellow
Write-Host "开始清理和验证操作..." -ForegroundColor Yellow

# 设置错误处理
$ErrorActionPreference = "Continue"

# 验证函数
function Verify-FileMove {
    param(
        [string]$OriginalPath,
        [string]$NewPath,
        [string]$Description
    )
    
    $originalExists = Test-Path $OriginalPath
    $newExists = Test-Path $NewPath
    
    if ($newExists -and -not $originalExists) {
        Write-Host "✅ 验证通过: $Description" -ForegroundColor Green
        return "成功移动"
    }
    elseif ($newExists -and $originalExists) {
        Write-Host "⚠️  发现重复: $Description" -ForegroundColor Yellow
        Write-Host "   原始位置: $OriginalPath" -ForegroundColor Gray
        Write-Host "   新位置: $NewPath" -ForegroundColor Gray
        return "重复存在"
    }
    elseif (-not $newExists -and $originalExists) {
        Write-Host "❌ 移动失败: $Description" -ForegroundColor Red
        return "移动失败"
    }
    else {
        Write-Host "❌ 文件丢失: $Description" -ForegroundColor Red
        return "文件丢失"
    }
}

# 清理重复文件函数
function Clean-DuplicateFile {
    param(
        [string]$OriginalPath,
        [string]$NewPath,
        [string]$Description
    )
    
    if ((Test-Path $OriginalPath) -and (Test-Path $NewPath)) {
        try {
            # 比较文件大小
            $originalSize = (Get-Item $OriginalPath).Length
            $newSize = (Get-Item $NewPath).Length
            
            if ($originalSize -eq $newSize) {
                Write-Host "🔄 删除原始位置的重复文件: $Description" -ForegroundColor Yellow
                Remove-Item $OriginalPath -Force
                Write-Host "✅ 清理完成: $Description" -ForegroundColor Green
                return $true
            }
            else {
                Write-Host "⚠️  文件大小不同，跳过清理: $Description" -ForegroundColor Yellow
                Write-Host "   原始大小: $originalSize 字节" -ForegroundColor Gray
                Write-Host "   新文件大小: $newSize 字节" -ForegroundColor Gray
                return $false
            }
        }
        catch {
            Write-Host "❌ 清理失败: $Description - $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
    return $false
}

Write-Host "`n=== 第一阶段: 验证核心文件移动 ===" -ForegroundColor Cyan

# 定义所有文件映射
$allFileMappings = @{
    # 核心数据文件
    "NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv" = "02_Data_And_Analysis/Raw_Data/DATA_实验数据_NEL_G3P_25.08.02.csv"
    "G3P_OP60_破裂盘电阻焊接参数定义表.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_参数定义_G3P_OP60.xlsx"
    "失效模式定义.xlsx" = "02_Data_And_Analysis/Raw_Data/DATA_失效模式定义.xlsx"
    
    # 核心分析脚本
    "G3P_OP60_DOE_V7.1_Stage1_Analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_DOE_V7.1_Stage1_Analysis_v1.0.py"
    "g3p_comprehensive_statistical_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_综合统计分析_v1.0.py"
    "g3p_resistance_welding_analysis.py" = "02_Data_And_Analysis/Analysis_Scripts/SCRIPT_电阻焊接分析_v1.0.py"
    
    # 参考脚本
    "G3P_OP60_DOE_V7.1_Stage1_Simple_Analysis.py" = "02_Data_And_Analysis/Reference_Scripts/REF_简化分析_v1.0.py"
    "H1_Analysis_Manual.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1手动分析_v1.0.py"
    "H1_Data_Verification.py" = "02_Data_And_Analysis/Reference_Scripts/REF_H1数据验证_v1.0.py"
    
    # 可视化文件
    "g3p_data_exploration.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_数据探索_v1.0.png"
    "g3p_h1_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H1假设验证_v1.0.png"
    "g3p_h2_hypothesis_verification.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_H2假设验证_v1.0.png"
    "g3p_parameter_optimization.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_参数优化_v1.0.png"
    "g3p_prediction_models_v2.png" = "02_Data_And_Analysis/Results/Visualizations/RESULT_预测模型_v2.0.png"
    
    # 正式报告
    "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pdf" = "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pdf"
    "NE2024-0077 G3P Disk Holder Resistance Welding Optimization - Phase Summary Report.pptx" = "03_Official_Reports/REPORT_G3P_OP60_Phase_Summary_NE2024-0077.pptx"
    "Optimized Resistance Welding Parameters for G3P OP60 Disk Holders - 25.07.31.pdf" = "03_Official_Reports/REPORT_优化参数_G3P_OP60_20250731.pdf"
    
    # 设备文档
    "充气机电阻焊接说明书 - 英文版.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_充气机电阻焊接说明书_EN.pdf"
    "B0742300.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_B0742300.pdf"
    "B0984900.pdf" = "03_Official_Reports/Equipment_Documentation/EQUIP_B0984900.pdf"
}

# 验证统计
$verificationResults = @{
    "成功移动" = 0
    "重复存在" = 0
    "移动失败" = 0
    "文件丢失" = 0
}

$duplicateFiles = @()

Write-Host "验证文件移动状态..." -ForegroundColor White
foreach ($originalFile in $allFileMappings.Keys) {
    $newFile = $allFileMappings[$originalFile]
    $result = Verify-FileMove $originalFile $newFile $originalFile
    $verificationResults[$result]++
    
    if ($result -eq "重复存在") {
        $duplicateFiles += @{
            Original = $originalFile
            New = $newFile
            Description = $originalFile
        }
    }
}

Write-Host "`n📊 验证结果统计:" -ForegroundColor Yellow
foreach ($status in $verificationResults.Keys) {
    $count = $verificationResults[$status]
    $color = switch ($status) {
        "成功移动" { "Green" }
        "重复存在" { "Yellow" }
        "移动失败" { "Red" }
        "文件丢失" { "Red" }
    }
    Write-Host "  $status : $count" -ForegroundColor $color
}

Write-Host "`n=== 第二阶段: 清理重复文件 ===" -ForegroundColor Cyan

if ($duplicateFiles.Count -gt 0) {
    Write-Host "发现 $($duplicateFiles.Count) 个重复文件，开始清理..." -ForegroundColor Yellow
    
    $cleanedCount = 0
    foreach ($duplicate in $duplicateFiles) {
        if (Clean-DuplicateFile $duplicate.Original $duplicate.New $duplicate.Description) {
            $cleanedCount++
        }
    }
    
    Write-Host "`n✅ 清理完成，成功清理 $cleanedCount 个重复文件" -ForegroundColor Green
}
else {
    Write-Host "✅ 未发现重复文件，无需清理" -ForegroundColor Green
}

Write-Host "`n=== 第三阶段: 验证文件夹结构 ===" -ForegroundColor Cyan

$requiredFolders = @(
    "01_Core_Technical_Documents/Final_Analysis",
    "01_Core_Technical_Documents/Project_Management",
    "01_Core_Technical_Documents/Knowledge_Base",
    "02_Data_And_Analysis/Raw_Data",
    "02_Data_And_Analysis/Analysis_Scripts",
    "02_Data_And_Analysis/Reference_Scripts",
    "02_Data_And_Analysis/Results/Visualizations",
    "03_Official_Reports/Equipment_Documentation",
    "04_Reference_Documents/Historical_Analysis",
    "04_Reference_Documents/Process_Documentation",
    "04_Reference_Documents/Site_Photos",
    "05_Archive/Deprecated_Files/Early_Analysis",
    "05_Archive/Deprecated_Files/Duplicate_Scripts",
    "05_Archive/Deprecated_Files/Unrelated_Files"
)

$folderStatus = @{
    "存在" = 0
    "缺失" = 0
}

Write-Host "验证文件夹结构..." -ForegroundColor White
foreach ($folder in $requiredFolders) {
    if (Test-Path $folder) {
        Write-Host "✅ $folder" -ForegroundColor Green
        $folderStatus["存在"]++
    }
    else {
        Write-Host "❌ $folder" -ForegroundColor Red
        $folderStatus["缺失"]++
        
        # 尝试创建缺失的文件夹
        try {
            New-Item -Path $folder -ItemType Directory -Force | Out-Null
            Write-Host "   ✅ 已创建缺失文件夹" -ForegroundColor Green
            $folderStatus["存在"]++
            $folderStatus["缺失"]--
        }
        catch {
            Write-Host "   ❌ 无法创建文件夹: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n📊 文件夹结构统计:" -ForegroundColor Yellow
Write-Host "  存在: $($folderStatus['存在'])" -ForegroundColor Green
Write-Host "  缺失: $($folderStatus['缺失'])" -ForegroundColor Red

Write-Host "`n=== 第四阶段: 生成最终验证报告 ===" -ForegroundColor Cyan

$finalReport = @"
# G3P OP60项目文件重组最终验证报告

**验证时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
**验证状态**: $(if ($verificationResults['移动失败'] -eq 0 -and $verificationResults['文件丢失'] -eq 0) { "✅ 验证通过" } else { "⚠️ 发现问题" })

## 📊 文件移动验证结果

- **成功移动**: $($verificationResults['成功移动']) 个文件
- **重复存在**: $($verificationResults['重复存在']) 个文件 $(if ($verificationResults['重复存在'] -gt 0) { "(已清理)" } else { "" })
- **移动失败**: $($verificationResults['移动失败']) 个文件
- **文件丢失**: $($verificationResults['文件丢失']) 个文件

## 📁 文件夹结构验证

- **文件夹总数**: $($requiredFolders.Count)
- **存在**: $($folderStatus['存在'])
- **缺失**: $($folderStatus['缺失'])

## 🎯 重组完成度评估

### 总体完成度
- **文件移动**: $(if ($verificationResults['成功移动'] -gt 15) { "✅ 优秀" } elseif ($verificationResults['成功移动'] -gt 10) { "🔶 良好" } else { "⚠️ 需要改进" })
- **文件夹结构**: $(if ($folderStatus['缺失'] -eq 0) { "✅ 完整" } else { "⚠️ 不完整" })
- **清理状态**: $(if ($duplicateFiles.Count -eq 0) { "✅ 无重复" } else { "🔶 已清理重复" })

### 建议后续操作

$(if ($verificationResults['移动失败'] -gt 0 -or $verificationResults['文件丢失'] -gt 0) {
"#### 🔧 需要修复的问题
1. 检查移动失败的文件，可能需要手动移动
2. 查找丢失的文件，确认是否在其他位置
3. 重新运行iCloud兼容移动脚本"
} else {
"#### ✅ 重组成功完成
1. 运行文档更新脚本更新引用
2. 开始使用新的文档管理体系
3. 享受高效的文件组织结构"
})

## 📋 详细文件清单

### 成功移动的文件
$(foreach ($file in $allFileMappings.Keys) {
    $newPath = $allFileMappings[$file]
    if ((Test-Path $newPath) -and -not (Test-Path $file)) {
        "- ✅ $file → $newPath"
    }
})

### 需要注意的文件
$(foreach ($file in $allFileMappings.Keys) {
    $newPath = $allFileMappings[$file]
    if (-not (Test-Path $newPath) -or (Test-Path $file)) {
        $status = if (-not (Test-Path $newPath) -and (Test-Path $file)) { "❌ 移动失败" } 
                 elseif ((Test-Path $newPath) -and (Test-Path $file)) { "⚠️ 重复存在" }
                 else { "❌ 文件丢失" }
        "- $status : $file"
    }
})

---
**验证完成时间**: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
**下一步**: $(if ($verificationResults['移动失败'] -eq 0 -and $verificationResults['文件丢失'] -eq 0) { "运行文档更新脚本" } else { "修复发现的问题" })
"@

Set-Content -Path "FINAL_VERIFICATION_REPORT.md" -Value $finalReport -Encoding UTF8

Write-Host "`n=== 清理和验证完成 ===" -ForegroundColor Green
Write-Host "最终验证报告已保存到: FINAL_VERIFICATION_REPORT.md" -ForegroundColor Green

if ($verificationResults['移动失败'] -eq 0 -and $verificationResults['文件丢失'] -eq 0) {
    Write-Host "🎉 文件重组验证通过！可以继续下一步操作" -ForegroundColor Green
}
else {
    Write-Host "⚠️  发现问题，请查看验证报告并进行修复" -ForegroundColor Yellow
}
