#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查G3P OP60数据中的H1验证实验数据
"""

import pandas as pd
import numpy as np

def check_h1_verification():
    """检查H1验证实验数据"""
    
    # 读取数据
    file_path = r'C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv'
    df = pd.read_csv(file_path, skip_blank_lines=True)
    
    # 过滤掉空行和无效数据
    df = df[df['no.'].notna() & (df['no.'] != '#N/A')]
    
    print("=== 检查H1验证实验数据 ===")
    
    # 1. 检查verification列
    print("\n1. 检查verification列:")
    verification_samples = df[df['verification'].notna()]
    print(f"有verification标记的样品数: {len(verification_samples)}")
    
    if len(verification_samples) > 0:
        print("verification样品详情:")
        for i, row in verification_samples.iterrows():
            print(f"  {row['sample_id']}: verification={row['verification']}, S1电流={row['s1_percent_current']}")
    
    # 2. 检查group列
    print("\n2. 检查group列:")
    unique_groups = df['group#'].unique()
    print(f"所有组别: {unique_groups}")
    
    # 3. 查看是否有专门的H1验证样品
    print("\n3. 检查H1验证样品:")
    
    # 查找可能的H1验证样品
    h1_candidates = df[df['s1_percent_current'].isin([97, 99])]
    print(f"S1电流为97或99的样品数: {len(h1_candidates)}")
    
    # 检查这些样品的详细信息
    if len(h1_candidates) > 0:
        print("\n这些样品的详细信息:")
        for i, row in h1_candidates.head(10).iterrows():
            print(f"  {row['sample_id']}: S1={row['s1_percent_current']}%, 失效类型={row['weld_failure_type']}, 高度={row['post_weld_disk_holder_height']}")
    
    # 4. 检查是否有专门的验证实验样品
    print("\n4. 检查验证实验样品:")
    
    # 查找可能的验证实验样品（可能是最近生产的）
    recent_samples = df.sort_values('date_time', ascending=False).head(30)
    print(f"最近30个样品的S1电流分布:")
    
    s1_counts = recent_samples['s1_percent_current'].value_counts()
    for s1, count in s1_counts.items():
        print(f"  {s1}%: {count}个")
    
    # 检查这些样品的失效类型
    print(f"\n最近30个样品的失效类型分布:")
    failure_counts = recent_samples['weld_failure_type'].value_counts()
    for failure_type, count in failure_counts.items():
        print(f"  Type {failure_type}: {count}个")
    
    # 5. 查看是否有可能是专门的H1验证实验
    print("\n5. 寻找可能的H1验证实验:")
    
    # 查找连续的样品，它们可能是专门生产的验证样品
    print("检查连续的样品序列:")
    
    # 按日期排序
    df_sorted = df.sort_values('date_time')
    
    # 查找S1电流为97或99的连续样品
    h1_continuous = []
    consecutive_count = 0
    
    for i, row in df_sorted.iterrows():
        if row['s1_percent_current'] in [97, 99]:
            consecutive_count += 1
            h1_continuous.append(row)
        else:
            if consecutive_count >= 5:  # 如果连续5个以上样品都是97或99电流
                print(f"发现连续{consecutive_count}个H1验证样品:")
                for sample in h1_continuous[-consecutive_count:]:
                    print(f"  {sample['sample_id']}: {sample['s1_percent_current']}%, Type {sample['weld_failure_type']}")
            consecutive_count = 0
    
    # 检查最后一批
    if consecutive_count >= 5:
        print(f"发现连续{consecutive_count}个H1验证样品:")
        for sample in h1_continuous[-consecutive_count:]:
            print(f"  {sample['sample_id']}: {sample['s1_percent_current']}%, Type {sample['weld_failure_type']}")

if __name__ == "__main__":
    check_h1_verification()