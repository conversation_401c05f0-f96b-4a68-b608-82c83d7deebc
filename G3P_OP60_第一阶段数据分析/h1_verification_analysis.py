#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 第一阶段H1假设验证数据分析
分析S1电流97% vs 99%对焊接质量的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import ttest_ind, mannwhitneyu, chi2_contingency
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
def load_data():
    """加载并预处理数据"""
    file_path = r"C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv"
    
    # 读取CSV文件，跳过空行
    df = pd.read_csv(file_path, skip_blank_lines=True)
    
    # 过滤掉空行和无效数据
    df = df[df['no.'].notna() & (df['no.'] != '#N/A')]
    
    # 转换数据类型
    numeric_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b', 
                   'disk_gap_a', 'disk_gap_b', 'room_temperature', 'gas_fill_pressure',
                   'electrode_pressure', 'electrode_height']
    
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 提取S1电流百分比
    df['s1_current_pct'] = pd.to_numeric(df['s1_percent_current'], errors='coerce')
    
    # 创建质量状态列
    df['is_pass'] = (df['leakage'] == 'Pass').astype(int)
    df['is_crack'] = (df['weld_failure_type'] == 8).astype(int)
    df['is_height_defect'] = (df['weld_failure_type'] == 4).astype(int)
    
    # 计算平均焊缝宽度
    df['avg_weld_width'] = (df['weld_width_a'] + df['weld_width_b']) / 2
    
    return df

# 分析第一阶段H1验证数据
def analyze_h1_verification(df):
    """分析第一阶段H1假设验证数据"""
    
    # 筛选第一阶段数据（H1-01到H1-30）
    h1_samples = df[df['sample_id'].str.startswith('H1-', na=False)]
    
    print("=== G3P OP60 第一阶段H1假设验证数据分析 ===\n")
    print(f"第一阶段总样品数: {len(h1_samples)}")
    
    # 按S1电流分组
    group_97 = h1_samples[h1_samples['s1_current_pct'] == 97]
    group_99 = h1_samples[h1_samples['s1_current_pct'] == 99]
    
    print(f"97%电流组样品数: {len(group_97)}")
    print(f"99%电流组样品数: {len(group_99)}")
    
    # 1. 焊接高度分析
    print("\n=== 焊接高度分析 ===")
    height_97 = group_97['post_weld_disk_holder_height'].dropna()
    height_99 = group_99['post_weld_disk_holder_height'].dropna()
    
    print(f"97%组平均高度: {height_97.mean():.3f}±{height_97.std():.3f} mm")
    print(f"99%组平均高度: {height_99.mean():.3f}±{height_99.std():.3f} mm")
    
    # 统计检验
    if len(height_97) > 1 and len(height_99) > 1:
        t_stat, p_value = ttest_ind(height_97, height_99)
        print(f"t检验结果: t={t_stat:.3f}, p={p_value:.4f}")
        
        if p_value < 0.05:
            print("✓ 两组高度存在显著差异")
        else:
            print("✗ 两组高度无显著差异")
    
    # 2. 失效类型分析
    print("\n=== 失效类型分析 ===")
    
    # 高度不良
    height_defect_97 = group_97['is_height_defect'].sum()
    height_defect_99 = group_99['is_height_defect'].sum()
    print(f"97%组高度不良: {height_defect_97}/{len(group_97)} ({height_defect_97/len(group_97)*100:.1f}%)")
    print(f"99%组高度不良: {height_defect_99}/{len(group_99)} ({height_defect_99/len(group_99)*100:.1f}%)")
    
    # 裂纹失效
    crack_97 = group_97['is_crack'].sum()
    crack_99 = group_99['is_crack'].sum()
    print(f"97%组裂纹失效: {crack_97}/{len(group_97)} ({crack_97/len(group_97)*100:.1f}%)")
    print(f"99%组裂纹失效: {crack_99}/{len(group_99)} ({crack_99/len(group_99)*100:.1f}%)")
    
    # 卡方检验
    if len(group_97) > 0 and len(group_99) > 0:
        # 高度不良卡方检验
        height_contingency = [[height_defect_97, len(group_97)-height_defect_97],
                             [height_defect_99, len(group_99)-height_defect_99]]
        chi2, p_height, dof, expected = chi2_contingency(height_contingency)
        print(f"高度不良卡方检验: χ²={chi2:.3f}, p={p_height:.4f}")
        
        # 裂纹失效卡方检验
        crack_contingency = [[crack_97, len(group_97)-crack_97],
                           [crack_99, len(group_99)-crack_99]]
        chi2, p_crack, dof, expected = chi2_contingency(crack_contingency)
        print(f"裂纹失效卡方检验: χ²={chi2:.3f}, p={p_crack:.4f}")
        
        if p_crack < 0.05:
            print("✓ H1假设验证通过：S1电流对裂纹失效有显著影响")
        else:
            print("✗ H1假设未得到验证")
    
    return h1_samples, group_97, group_99

# 生成分析图表
def generate_analysis_plots(h1_samples, group_97, group_99):
    """生成分析图表"""
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('G3P OP60 第一阶段H1假设验证分析', fontsize=16, fontweight='bold')
    
    # 1. 焊接高度分布对比
    ax1 = axes[0, 0]
    height_97 = group_97['post_weld_disk_holder_height'].dropna()
    height_99 = group_99['post_weld_disk_holder_height'].dropna()
    
    ax1.hist(height_97, alpha=0.7, label='97%电流', bins=15, color='blue')
    ax1.hist(height_99, alpha=0.7, label='99%电流', bins=15, color='red')
    ax1.set_xlabel('焊接高度 (mm)')
    ax1.set_ylabel('频次')
    ax1.set_title('焊接高度分布对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 失效类型对比
    ax2 = axes[0, 1]
    failure_types = ['高度不良', '裂纹失效']
    rates_97 = [
        group_97['is_height_defect'].mean() * 100,
        group_97['is_crack'].mean() * 100
    ]
    rates_99 = [
        group_99['is_height_defect'].mean() * 100,
        group_99['is_crack'].mean() * 100
    ]
    
    x = np.arange(len(failure_types))
    width = 0.35
    
    ax2.bar(x - width/2, rates_97, width, label='97%电流', color='blue', alpha=0.7)
    ax2.bar(x + width/2, rates_99, width, label='99%电流', color='red', alpha=0.7)
    ax2.set_xlabel('失效类型')
    ax2.set_ylabel('失效率 (%)')
    ax2.set_title('失效类型对比')
    ax2.set_xticks(x)
    ax2.set_xticklabels(failure_types)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 参数执行情况
    ax3 = axes[1, 0]
    param_cols = ['electrode_pressure', 'gas_fill_pressure', 'room_temperature']
    param_labels = ['电极压力 (psi)', '充气压力 (psi)', '环境温度 (°C)']
    
    for i, (col, label) in enumerate(zip(param_cols, param_labels)):
        mean_97 = group_97[col].mean()
        mean_99 = group_99[col].mean()
        ax3.scatter([i-0.1], [mean_97], color='blue', s=100, label='97%' if i==0 else "")
        ax3.scatter([i+0.1], [mean_99], color='red', s=100, label='99%' if i==0 else "")
    
    ax3.set_xlabel('参数类型')
    ax3.set_ylabel('平均值')
    ax3.set_title('关键参数执行情况')
    ax3.set_xticks(range(len(param_labels)))
    ax3.set_xticklabels([label.split(' ')[0] for label in param_labels])
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 样品执行顺序
    ax4 = axes[1, 1]
    execution_order = range(1, len(h1_samples) + 1)
    heights = h1_samples['post_weld_disk_holder_height'].values
    colors = ['red' if s1 == 99 else 'blue' for s1 in h1_samples['s1_current_pct'].values]
    
    ax4.scatter(execution_order, heights, c=colors, alpha=0.7, s=60)
    ax4.set_xlabel('执行顺序')
    ax4.set_ylabel('焊接高度 (mm)')
    ax4.set_title('随机执行顺序与高度分布')
    ax4.grid(True, alpha=0.3)
    
    # 添加图例
    from matplotlib.lines import Line2D
    legend_elements = [Line2D([0], [0], marker='o', color='w', markerfacecolor='blue', markersize=8, label='97%电流'),
                     Line2D([0], [0], marker='o', color='w', markerfacecolor='red', markersize=8, label='99%电流')]
    ax4.legend(handles=legend_elements)
    
    plt.tight_layout()
    
    # 保存图表
    plot_path = r"C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\G3P_OP60_第一阶段数据分析\H1_验证分析图表.png"
    plt.savefig(plot_path, dpi=300, bbox_inches='tight')
    print(f"\n分析图表已保存至: {plot_path}")
    
    return fig

# 生成统计报告
def generate_statistical_report(h1_samples, group_97, group_99):
    """生成统计报告"""
    
    report_path = r"C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\G3P_OP60_第一阶段数据分析\H1验证统计报告.md"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# G3P OP60 第一阶段H1假设验证统计报告\n\n")
        f.write(f"**分析日期**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 执行摘要\n")
        f.write(f"- **总样品数**: {len(h1_samples)}\n")
        f.write(f"- **97%电流组**: {len(group_97)} 样品\n")
        f.write(f"- **99%电流组**: {len(group_99)} 样品\n\n")
        
        # 高度分析
        height_97 = group_97['post_weld_disk_holder_height'].dropna()
        height_99 = group_99['post_weld_disk_holder_height'].dropna()
        
        f.write("## 焊接高度分析\n")
        f.write(f"### 97%电流组\n")
        f.write(f"- 平均值: {height_97.mean():.3f} mm\n")
        f.write(f"- 标准差: {height_97.std():.3f} mm\n")
        f.write(f"- 最小值: {height_97.min():.3f} mm\n")
        f.write(f"- 最大值: {height_97.max():.3f} mm\n\n")
        
        f.write(f"### 99%电流组\n")
        f.write(f"- 平均值: {height_99.mean():.3f} mm\n")
        f.write(f"- 标准差: {height_99.std():.3f} mm\n")
        f.write(f"- 最小值: {height_99.min():.3f} mm\n")
        f.write(f"- 最大值: {height_99.max():.3f} mm\n\n")
        
        # 失效分析
        f.write("## 失效分析\n")
        f.write("### 高度不良\n")
        height_defect_97 = group_97['is_height_defect'].sum()
        height_defect_99 = group_99['is_height_defect'].sum()
        f.write(f"- 97%组: {height_defect_97}/{len(group_97)} ({height_defect_97/len(group_97)*100:.1f}%)\n")
        f.write(f"- 99%组: {height_defect_99}/{len(group_99)} ({height_defect_99/len(group_99)*100:.1f}%)\n\n")
        
        f.write("### 裂纹失效\n")
        crack_97 = group_97['is_crack'].sum()
        crack_99 = group_99['is_crack'].sum()
        f.write(f"- 97%组: {crack_97}/{len(group_97)} ({crack_97/len(group_97)*100:.1f}%)\n")
        f.write(f"- 99%组: {crack_99}/{len(group_99)} ({crack_99/len(group_99)*100:.1f}%)\n\n")
        
        # 参数执行情况
        f.write("## 参数执行情况\n")
        for param in ['electrode_pressure', 'gas_fill_pressure', 'room_temperature']:
            f.write(f"### {param}\n")
            f.write(f"- 97%组: {group_97[param].mean():.1f} ± {group_97[param].std():.1f}\n")
            f.write(f"- 99%组: {group_99[param].mean():.1f} ± {group_99[param].std():.1f}\n\n")
        
        # 结论
        f.write("## 结论与建议\n")
        f.write("### H1假设验证结果\n")
        
        # 计算统计显著性
        if len(height_97) > 1 and len(height_99) > 1:
            t_stat, p_value = ttest_ind(height_97, height_99)
            if p_value < 0.05:
                f.write("- ✅ **高度差异显著**: p={p_value:.4f} < 0.05\n")
            else:
                f.write("- ❌ **高度差异不显著**: p={p_value:.4f} > 0.05\n")
        
        if len(group_97) > 0 and len(group_99) > 0:
            crack_contingency = [[crack_97, len(group_97)-crack_97],
                               [crack_99, len(group_99)-crack_99]]
            chi2, p_crack, dof, expected = chi2_contingency(crack_contingency)
            if p_crack < 0.05:
                f.write("- ✅ **裂纹差异显著**: p={p_crack:.4f} < 0.05\n")
                f.write("- ✅ **H1假设验证通过**: S1电流对裂纹失效有显著影响\n")
            else:
                f.write("- ❌ **裂纹差异不显著**: p={p_crack:.4f} > 0.05\n")
                f.write("- ❌ **H1假设未得到验证**: 需要更多数据或调整实验设计\n")
        
        f.write("\n### 第二阶段建议\n")
        f.write("- 基于验证结果，建议第二阶段S1电流固定在最优水平\n")
        f.write("- 继续执行CCD响应面建模实验\n")
        f.write("- 重点关注显著影响因子的优化范围\n")
    
    print(f"统计报告已保存至: {report_path}")

# 主函数
def main():
    """主分析流程"""
    
    # 加载数据
    df = load_data()
    
    # 分析H1验证数据
    h1_samples, group_97, group_99 = analyze_h1_verification(df)
    
    # 生成分析图表
    fig = generate_analysis_plots(h1_samples, group_97, group_99)
    
    # 生成统计报告
    generate_statistical_report(h1_samples, group_97, group_99)
    
    print("\n=== 分析完成 ===")
    print("请查看生成的分析图表和统计报告")

if __name__ == "__main__":
    main()