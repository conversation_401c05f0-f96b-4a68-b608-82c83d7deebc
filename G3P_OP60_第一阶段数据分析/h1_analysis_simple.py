#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 第一阶段H1假设验证数据分析 (简化版)
分析S1电流97% vs 99%对焊接质量的影响
"""

import pandas as pd
import numpy as np
from scipy import stats
from scipy.stats import ttest_ind, mannwhitneyu, chi2_contingency
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """加载并预处理数据"""
    file_path = r"C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv"
    
    # 读取CSV文件，跳过空行
    df = pd.read_csv(file_path, skip_blank_lines=True)
    
    # 过滤掉空行和无效数据
    df = df[df['no.'].notna() & (df['no.'] != '#N/A')]
    
    # 转换数据类型
    numeric_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b', 
                   'disk_gap_a', 'disk_gap_b', 'room_temperature', 'gas_fill_pressure',
                   'electrode_pressure', 'electrode_height']
    
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 提取S1电流百分比
    df['s1_current_pct'] = pd.to_numeric(df['s1_percent_current'], errors='coerce')
    
    # 创建质量状态列
    df['is_pass'] = (df['leakage'] == 'Pass').astype(int)
    df['is_crack'] = (df['weld_failure_type'] == 8).astype(int)
    df['is_height_defect'] = (df['weld_failure_type'] == 4).astype(int)
    
    # 计算平均焊缝宽度
    df['avg_weld_width'] = (df['weld_width_a'] + df['weld_width_b']) / 2
    
    return df

def analyze_h1_verification(df):
    """分析第一阶段H1假设验证数据"""
    
    # 筛选第一阶段数据（H1-01到H1-30）
    h1_samples = df[df['sample_id'].str.startswith('H1-', na=False)]
    
    print("=== G3P OP60 第一阶段H1假设验证数据分析 ===\n")
    print(f"第一阶段总样品数: {len(h1_samples)}")
    
    # 按S1电流分组
    group_97 = h1_samples[h1_samples['s1_current_pct'] == 97]
    group_99 = h1_samples[h1_samples['s1_current_pct'] == 99]
    
    print(f"97%电流组样品数: {len(group_97)}")
    print(f"99%电流组样品数: {len(group_99)}")
    
    # 1. 焊接高度分析
    print("\n=== 焊接高度分析 ===")
    height_97 = group_97['post_weld_disk_holder_height'].dropna()
    height_99 = group_99['post_weld_disk_holder_height'].dropna()
    
    print(f"97%组平均高度: {height_97.mean():.3f}±{height_97.std():.3f} mm")
    print(f"99%组平均高度: {height_99.mean():.3f}±{height_99.std():.3f} mm")
    print(f"97%组高度范围: {height_97.min():.3f} - {height_97.max():.3f} mm")
    print(f"99%组高度范围: {height_99.min():.3f} - {height_99.max():.3f} mm")
    
    # 统计检验
    if len(height_97) > 1 and len(height_99) > 1:
        t_stat, p_value = ttest_ind(height_97, height_99)
        print(f"t检验结果: t={t_stat:.3f}, p={p_value:.4f}")
        
        if p_value < 0.05:
            print("✓ 两组高度存在显著差异")
        else:
            print("✗ 两组高度无显著差异")
    
    # 2. 失效类型分析
    print("\n=== 失效类型分析 ===")
    
    # 高度不良
    height_defect_97 = group_97['is_height_defect'].sum()
    height_defect_99 = group_99['is_height_defect'].sum()
    print(f"97%组高度不良: {height_defect_97}/{len(group_97)} ({height_defect_97/len(group_97)*100:.1f}%)")
    print(f"99%组高度不良: {height_defect_99}/{len(group_99)} ({height_defect_99/len(group_99)*100:.1f}%)")
    
    # 裂纹失效
    crack_97 = group_97['is_crack'].sum()
    crack_99 = group_99['is_crack'].sum()
    print(f"97%组裂纹失效: {crack_97}/{len(group_97)} ({crack_97/len(group_97)*100:.1f}%)")
    print(f"99%组裂纹失效: {crack_99}/{len(group_99)} ({crack_99/len(group_99)*100:.1f}%)")
    
    # 3. 泄漏测试分析
    print("\n=== 泄漏测试分析 ===")
    leak_data_97 = group_97[group_97['leakage'].notna()]
    leak_data_99 = group_99[group_99['leakage'].notna()]
    
    leak_pass_97 = (leak_data_97['leakage'] == 'Pass').sum()
    leak_pass_99 = (leak_data_99['leakage'] == 'Pass').sum()
    
    print(f"97%组泄漏测试通过: {leak_pass_97}/{len(leak_data_97)} ({leak_pass_97/len(leak_data_97)*100:.1f}%)")
    print(f"99%组泄漏测试通过: {leak_pass_99}/{len(leak_data_99)} ({leak_pass_99/len(leak_data_99)*100:.1f}%)")
    
    # 4. 统计检验
    print("\n=== 统计检验结果 ===")
    
    if len(group_97) > 0 and len(group_99) > 0:
        # 高度不良卡方检验
        height_contingency = [[height_defect_97, len(group_97)-height_defect_97],
                             [height_defect_99, len(group_99)-height_defect_99]]
        chi2, p_height, dof, expected = chi2_contingency(height_contingency)
        print(f"高度不良卡方检验: χ²={chi2:.3f}, p={p_height:.4f}")
        
        # 裂纹失效卡方检验
        crack_contingency = [[crack_97, len(group_97)-crack_97],
                           [crack_99, len(group_99)-crack_99]]
        chi2, p_crack, dof, expected = chi2_contingency(crack_contingency)
        print(f"裂纹失效卡方检验: χ²={chi2:.3f}, p={p_crack:.4f}")
        
        # 泄漏测试卡方检验
        if len(leak_data_97) > 0 and len(leak_data_99) > 0:
            leak_contingency = [[leak_pass_97, len(leak_data_97)-leak_pass_97],
                               [leak_pass_99, len(leak_data_99)-leak_pass_99]]
            chi2, p_leak, dof, expected = chi2_contingency(leak_contingency)
            print(f"泄漏测试卡方检验: χ²={chi2:.3f}, p={p_leak:.4f}")
        
        # H1假设验证结论
        print("\n=== H1假设验证结论 ===")
        if p_crack < 0.05:
            print("✅ H1假设验证通过：S1电流对裂纹失效有显著影响")
            if crack_97 < crack_99:
                print("✅ 97%电流比99%电流更安全（裂纹率更低）")
            else:
                print("⚠️ 99%电流比97%电流更安全（裂纹率更低）")
        else:
            print("❌ H1假设未得到验证：S1电流对裂纹失效影响不显著")
    
    # 5. 参数执行情况分析
    print("\n=== 参数执行情况分析 ===")
    param_cols = ['electrode_pressure', 'gas_fill_pressure', 'room_temperature']
    param_labels = ['电极压力', '充气压力', '环境温度']
    
    for col, label in zip(param_cols, param_labels):
        if col in group_97.columns and col in group_99.columns:
            mean_97 = group_97[col].mean()
            std_97 = group_97[col].std()
            mean_99 = group_99[col].mean()
            std_99 = group_99[col].std()
            
            print(f"{label}:")
            print(f"  97%组: {mean_97:.1f} ± {std_97:.1f}")
            print(f"  99%组: {mean_99:.1f} ± {std_99:.1f}")
            
            # 检验参数是否有显著差异
            if len(group_97[col].dropna()) > 1 and len(group_99[col].dropna()) > 1:
                t_stat, p_param = ttest_ind(group_97[col].dropna(), group_99[col].dropna())
                if p_param < 0.05:
                    print(f"  ⚠️ 参数存在显著差异 (p={p_param:.4f})")
                else:
                    print(f"  ✓ 参数控制良好 (p={p_param:.4f})")
            print()
    
    return h1_samples, group_97, group_99

def generate_detailed_report(h1_samples, group_97, group_99):
    """生成详细分析报告"""
    
    report_path = r"C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\G3P_OP60_第一阶段数据分析\H1验证详细报告.txt"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("G3P OP60 第一阶段H1假设验证详细分析报告\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("1. 样本概况\n")
        f.write("-" * 20 + "\n")
        f.write(f"总样品数: {len(h1_samples)}\n")
        f.write(f"97%电流组: {len(group_97)} 样品\n")
        f.write(f"99%电流组: {len(group_99)} 样品\n\n")
        
        # 详细高度分析
        f.write("2. 焊接高度详细分析\n")
        f.write("-" * 20 + "\n")
        height_97 = group_97['post_weld_disk_holder_height'].dropna()
        height_99 = group_99['post_weld_disk_holder_height'].dropna()
        
        f.write("2.1 97%电流组高度数据:\n")
        f.write(f"   样本数: {len(height_97)}\n")
        f.write(f"   平均值: {height_97.mean():.3f} mm\n")
        f.write(f"   标准差: {height_97.std():.3f} mm\n")
        f.write(f"   最小值: {height_97.min():.3f} mm\n")
        f.write(f"   最大值: {height_97.max():.3f} mm\n")
        f.write(f"   中位数: {height_97.median():.3f} mm\n")
        f.write(f"   95%置信区间: {height_97.mean() - 1.96*height_97.std()/np.sqrt(len(height_97)):.3f} - {height_97.mean() + 1.96*height_97.std()/np.sqrt(len(height_97)):.3f} mm\n\n")
        
        f.write("2.2 99%电流组高度数据:\n")
        f.write(f"   样本数: {len(height_99)}\n")
        f.write(f"   平均值: {height_99.mean():.3f} mm\n")
        f.write(f"   标准差: {height_99.std():.3f} mm\n")
        f.write(f"   最小值: {height_99.min():.3f} mm\n")
        f.write(f"   最大值: {height_99.max():.3f} mm\n")
        f.write(f"   中位数: {height_99.median():.3f} mm\n")
        f.write(f"   95%置信区间: {height_99.mean() - 1.96*height_99.std()/np.sqrt(len(height_99)):.3f} - {height_99.mean() + 1.96*height_99.std()/np.sqrt(len(height_99)):.3f} mm\n\n")
        
        # 失效分析
        f.write("3. 失效模式分析\n")
        f.write("-" * 20 + "\n")
        
        height_defect_97 = group_97['is_height_defect'].sum()
        height_defect_99 = group_99['is_height_defect'].sum()
        crack_97 = group_97['is_crack'].sum()
        crack_99 = group_99['is_crack'].sum()
        
        f.write("3.1 高度不良分析:\n")
        f.write(f"   97%组: {height_defect_97}/{len(group_97)} ({height_defect_97/len(group_97)*100:.1f}%)\n")
        f.write(f"   99%组: {height_defect_99}/{len(group_99)} ({height_defect_99/len(group_99)*100:.1f}%)\n")
        
        if len(group_97) > 0 and len(group_99) > 0:
            height_contingency = [[height_defect_97, len(group_97)-height_defect_97],
                                 [height_defect_99, len(group_99)-height_defect_99]]
            chi2, p_height, dof, expected = chi2_contingency(height_contingency)
            f.write(f"   卡方检验: χ²={chi2:.3f}, p={p_height:.4f}\n")
            if p_height < 0.05:
                f.write("   结论: 两组高度不良率存在显著差异\n")
            else:
                f.write("   结论: 两组高度不良率无显著差异\n")
        f.write("\n")
        
        f.write("3.2 裂纹失效分析:\n")
        f.write(f"   97%组: {crack_97}/{len(group_97)} ({crack_97/len(group_97)*100:.1f}%)\n")
        f.write(f"   99%组: {crack_99}/{len(group_99)} ({crack_99/len(group_99)*100:.1f}%)\n")
        
        if len(group_97) > 0 and len(group_99) > 0:
            crack_contingency = [[crack_97, len(group_97)-crack_97],
                               [crack_99, len(group_99)-crack_99]]
            chi2, p_crack, dof, expected = chi2_contingency(crack_contingency)
            f.write(f"   卡方检验: χ²={chi2:.3f}, p={p_crack:.4f}\n")
            if p_crack < 0.05:
                f.write("   结论: 两组裂纹率存在显著差异\n")
                if crack_97 < crack_99:
                    f.write("   建议: 97%电流更安全，裂纹率更低\n")
                else:
                    f.write("   建议: 99%电流更安全，裂纹率更低\n")
            else:
                f.write("   结论: 两组裂纹率无显著差异\n")
        f.write("\n")
        
        # 参数控制分析
        f.write("4. 参数控制分析\n")
        f.write("-" * 20 + "\n")
        
        param_cols = ['electrode_pressure', 'gas_fill_pressure', 'room_temperature']
        param_labels = ['电极压力(psi)', '充气压力(psi)', '环境温度(°C)']
        
        for col, label in zip(param_cols, param_labels):
            if col in group_97.columns:
                f.write(f"4.{param_cols.index(col)+1} {label}:\n")
                f.write(f"   97%组: {group_97[col].mean():.1f} ± {group_97[col].std():.1f} (n={len(group_97[col].dropna())})\n")
                f.write(f"   99%组: {group_99[col].mean():.1f} ± {group_99[col].std():.1f} (n={len(group_99[col].dropna())})\n")
                
                if len(group_97[col].dropna()) > 1 and len(group_99[col].dropna()) > 1:
                    t_stat, p_param = ttest_ind(group_97[col].dropna(), group_99[col].dropna())
                    f.write(f"   t检验: t={t_stat:.3f}, p={p_param:.4f}\n")
                    if p_param < 0.05:
                        f.write("   警告: 参数控制存在显著差异\n")
                    else:
                        f.write("   状态: 参数控制良好\n")
                f.write("\n")
        
        # 结论与建议
        f.write("5. 结论与建议\n")
        f.write("-" * 20 + "\n")
        
        # H1假设验证结论
        if len(group_97) > 0 and len(group_99) > 0:
            crack_contingency = [[crack_97, len(group_97)-crack_97],
                               [crack_99, len(group_99)-crack_99]]
            chi2, p_crack, dof, expected = chi2_contingency(crack_contingency)
            
            f.write("5.1 H1假设验证结果:\n")
            if p_crack < 0.05:
                f.write("   ✅ H1假设验证通过\n")
                f.write("   ✅ S1电流对裂纹失效有显著影响\n")
                if crack_97 < crack_99:
                    f.write("   ✅ 97%电流比99%电流更安全\n")
                    f.write("   建议: 第二阶段使用97%S1电流\n")
                else:
                    f.write("   ✅ 99%电流比97%电流更安全\n")
                    f.write("   建议: 第二阶段使用99%S1电流\n")
            else:
                f.write("   ❌ H1假设未得到验证\n")
                f.write("   ❌ S1电流对裂纹失效影响不显著\n")
                f.write("   建议: 需要更多数据或重新设计实验\n")
        
        f.write("\n5.2 第二阶段执行建议:\n")
        f.write("   - 基于验证结果确定S1电流设定值\n")
        f.write("   - 严格按照V7.1执行指令进行CCD实验\n")
        f.write("   - 重点关注显著影响因子的优化范围\n")
        f.write("   - 继续收集金相和泄漏测试数据\n")
        
        f.write("\n5.3 质量控制建议:\n")
        f.write("   - 加强参数监控，确保执行精度\n")
        f.write("   - 完善数据记录，确保数据完整性\n")
        f.write("   - 定期进行统计分析，及时发现问题\n")
        
        f.write("\n报告生成时间: " + pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'))
    
    print(f"详细分析报告已保存至: {report_path}")

def main():
    """主分析流程"""
    
    # 加载数据
    df = load_data()
    
    # 分析H1验证数据
    h1_samples, group_97, group_99 = analyze_h1_verification(df)
    
    # 生成详细报告
    generate_detailed_report(h1_samples, group_97, group_99)
    
    print("\n=== 分析完成 ===")
    print("请查看生成的详细分析报告")

if __name__ == "__main__":
    main()