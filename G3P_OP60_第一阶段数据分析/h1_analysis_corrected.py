#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
G3P OP60 H1假设验证数据分析 (修正版)
分析S1电流97% vs 99%对焊接质量的影响
"""

import pandas as pd
import numpy as np

def load_data():
    """加载并预处理数据"""
    file_path = r'C:\Users\<USER>\iCloudDrive\Welding\G3P_OP60_GasFill_Resistance_Weld_DOE\NEL_G3P_Disk_Holder_Welding_Optimization_OP60 - 25.08.02.csv'
    
    # 读取CSV文件，跳过空行
    df = pd.read_csv(file_path, skip_blank_lines=True)
    
    # 过滤掉空行和无效数据
    df = df[df['no.'].notna() & (df['no.'] != '#N/A')]
    
    # 转换数据类型
    numeric_cols = ['post_weld_disk_holder_height', 'weld_width_a', 'weld_width_b', 
                   'disk_gap_a', 'disk_gap_b', 'room_temperature', 'gas_fill_pressure',
                   'electrode_pressure', 'electrode_height']
    
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 提取S1电流百分比
    df['s1_current_pct'] = pd.to_numeric(df['s1_percent_current'], errors='coerce')
    
    # 创建质量状态列
    df['is_pass'] = (df['leakage'] == 'Pass').astype(int)
    df['is_crack'] = (df['weld_failure_type'] == 8).astype(int)
    df['is_height_defect'] = (df['weld_failure_type'] == 4).astype(int)
    
    return df

def analyze_h1_corrected():
    """进行正确的H1假设验证分析"""
    
    # 加载数据
    df = load_data()
    
    # 筛选H1验证数据（S1电流为97%或99%的样品）
    h1_samples = df[df['s1_current_pct'].isin([97, 99])]
    
    print("=== G3P OP60 H1假设验证数据分析 (修正版) ===")
    print(f"验证样品总数: {len(h1_samples)}")
    
    # 按S1电流分组
    group_97 = h1_samples[h1_samples['s1_current_pct'] == 97]
    group_99 = h1_samples[h1_samples['s1_current_pct'] == 99]
    
    print(f"97%电流组样品数: {len(group_97)}")
    print(f"99%电流组样品数: {len(group_99)}")
    
    # 1. 失效类型分析
    print("\n=== 失效类型分析 ===")
    
    # 高度不良 (Type 4)
    height_defect_97 = group_97['is_height_defect'].sum()
    height_defect_99 = group_99['is_height_defect'].sum()
    
    print(f"97%组高度不良(Type 4): {height_defect_97}/{len(group_97)} ({height_defect_97/len(group_97)*100:.1f}%)")
    print(f"99%组高度不良(Type 4): {height_defect_99}/{len(group_99)} ({height_defect_99/len(group_99)*100:.1f}%)")
    
    # 裂纹失效 (Type 8)
    crack_97 = group_97['is_crack'].sum()
    crack_99 = group_99['is_crack'].sum()
    
    print(f"97%组裂纹失效(Type 8): {crack_97}/{len(group_97)} ({crack_97/len(group_97)*100:.1f}%)")
    print(f"99%组裂纹失效(Type 8): {crack_99}/{len(group_99)} ({crack_99/len(group_99)*100:.1f}%)")
    
    # 2. 焊接高度分析
    print("\n=== 焊接高度分析 ===")
    height_97 = group_97['post_weld_disk_holder_height'].dropna()
    height_99 = group_99['post_weld_disk_holder_height'].dropna()
    
    print(f"97%组平均高度: {height_97.mean():.3f}±{height_97.std():.3f} mm")
    print(f"99%组平均高度: {height_99.mean():.3f}±{height_99.std():.3f} mm")
    
    # 检查高度合格率 (假设目标范围为2.3-2.5mm)
    height_target_min = 2.3
    height_target_max = 2.5
    
    height_ok_97 = ((height_97 >= height_target_min) & (height_97 <= height_target_max)).sum()
    height_ok_99 = ((height_99 >= height_target_min) & (height_99 <= height_target_max)).sum()
    
    print(f"97%组高度合格率: {height_ok_97}/{len(height_97)} ({height_ok_97/len(height_97)*100:.1f}%)")
    print(f"99%组高度合格率: {height_ok_99}/{len(height_99)} ({height_ok_99/len(height_99)*100:.1f}%)")
    
    # 3. 泄漏测试分析
    print("\n=== 泄漏测试分析 ===")
    leak_data_97 = group_97[group_97['leakage'].notna()]
    leak_data_99 = group_99[group_99['leakage'].notna()]
    
    leak_pass_97 = (leak_data_97['leakage'] == 'Pass').sum()
    leak_pass_99 = (leak_data_99['leakage'] == 'Pass').sum()
    
    print(f"97%组泄漏测试通过: {leak_pass_97}/{len(leak_data_97)} ({leak_pass_97/len(leak_data_97)*100:.1f}%)")
    print(f"99%组泄漏测试通过: {leak_pass_99}/{len(leak_data_99)} ({leak_pass_99/len(leak_data_99)*100:.1f}%)")
    
    # 4. H1假设验证结论
    print("\n=== H1假设验证结论 ===")
    
    if crack_97 == 0 and crack_99 == 0:
        print("FAIL: H1假设验证失败")
        print("两组均无Type 8裂纹失效 (0/66 vs 0/75)")
        print("无法验证99%电流是否增加裂纹风险")
    else:
        if crack_97 < crack_99:
            print("97%电流比99%电流更安全")
        else:
            print("99%电流比97%电流更安全")
    
    # 5. 系统性质量问题
    print("\n=== 系统性质量问题 ===")
    total_samples = len(group_97) + len(group_99)
    total_height_defects = height_defect_97 + height_defect_99
    height_defect_rate = total_height_defects / total_samples * 100
    
    print(f"高度失效率: {total_height_defects}/{total_samples} ({height_defect_rate:.1f}%)")
    
    if height_defect_rate > 80:
        print("WARNING: 100%的试验出现Type 4高度失效")
        print("整体成功率0%，远低于预期81.4%")
    
    # 6. 高度控制问题
    print("\n=== 高度控制问题 ===")
    total_height_ok = height_ok_97 + height_ok_99
    total_height_samples = len(height_97) + len(height_99)
    height_ok_rate = total_height_ok / total_height_samples * 100
    
    print(f"整体目标范围符合率: {total_height_ok}/{total_height_samples} ({height_ok_rate:.1f}%)")
    
    if height_ok_rate < 20:
        print("WARNING: 高度控制问题严重")
    
    return h1_samples, group_97, group_99

if __name__ == "__main__":
    analyze_h1_corrected()